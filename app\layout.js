import { Inter } from 'next/font/google';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Footer from './components/footer';
import Navbar from './components/navbar';
import { ThemeProvider } from './contexts/ThemeContext';
import './css/card.scss';
import './css/globals.scss';
const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Portfolio of Kamal Abouzayed',
  description: "Greetings, I'm <PERSON>, a Backend Laravel Developer. With a passion for crafting exceptional applications, I specialize in Laravel, where I bring projects to life with precision and efficiency. My track record speaks for itself – delivering high-quality solutions that exceed expectations. Explore my portfolio and let's discuss how I can elevate your projects with my expertise in Laravel development.",
}

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ThemeProvider>
          <ToastContainer />
          <main className="min-h-screen relative mx-auto px-6 sm:px-12 lg:max-w-[70rem] xl:max-w-[76rem] 2xl:max-w-[92rem] text-gray-900 dark:text-white bg-white dark:bg-dark-bg transition-colors duration-300">
            <Navbar />
            {children}
          </main>
          <Footer />
        </ThemeProvider>
      </body>
    </html>
  )
};
