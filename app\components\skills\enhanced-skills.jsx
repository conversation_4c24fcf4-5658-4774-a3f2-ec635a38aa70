'use client';

import { skillsData } from "@/utils/data/skills";
import { skillsImage } from "@/utils/skill-image";
import Image from "next/image";
import { useState } from "react";
import { IntenseGlowCard, SubtleGlowCard } from "../helper/glow-card-variants";
import AnimationLottie from "../helper/animation-lottie";
import lottieFile from '/public/lottie/code.json';

const EnhancedSkills = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  
  // Group skills by category
  const categories = ['All', 'Backend', 'Frontend', 'Database', 'Tools', 'Other'];
  
  const getSkillCategory = (skill) => {
    const backendSkills = ['PHP', 'Laravel', 'Node JS', 'Express JS'];
    const frontendSkills = ['HTML', 'CSS', 'Javascript', 'TypeScript', 'React', 'Next JS', 'Vue', 'Nuxt JS', 'Tailwind'];
    const databaseSkills = ['MySQL', 'PostgreSQL', 'MongoDB'];
    const toolSkills = ['Git', 'Docker', 'AWS', 'Linux', 'Nginx'];
    
    if (backendSkills.includes(skill)) return 'Backend';
    if (frontendSkills.includes(skill)) return 'Frontend';
    if (databaseSkills.includes(skill)) return 'Database';
    if (toolSkills.includes(skill)) return 'Tools';
    return 'Other';
  };

  const filteredSkills = selectedCategory === 'All' 
    ? skillsData 
    : skillsData.filter(skill => getSkillCategory(skill) === selectedCategory);

  return (
    <div className="relative z-50 min-h-screen my-12 lg:my-24 skills-bg-animation">
      {/* Background Animation */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none opacity-5 dark:opacity-10">
        <AnimationLottie animationPath={lottieFile} />
      </div>

      {/* Floating particles effect */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 rounded-full bg-laravelRed/20 dark:bg-purple-500/20 animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Header Section with Intense Glow */}
      <div className="flex justify-center my-5 lg:py-8">
        <IntenseGlowCard identifier="skills-header">
          <div className="flex items-center px-8 py-6">
            <span className="w-24 h-[2px] bg-gradient-to-r from-laravelRed to-laravelOrange dark:from-purple-500 dark:to-pink-500 transition-colors duration-400"></span>
            <span className="p-4 px-8 mx-4 text-2xl font-bold text-white transition-colors rounded-lg shadow-lg bg-gradient-to-r from-laravelRed to-laravelOrange dark:from-purple-500 dark:to-pink-500 w-fit duration-400">
              Technical Skills
            </span>
            <span className="w-24 h-[2px] bg-gradient-to-r from-laravelOrange to-laravelRed dark:from-pink-500 dark:to-purple-500 transition-colors duration-400"></span>
          </div>
        </IntenseGlowCard>
      </div>

      {/* Category Filter with Intense Glow */}
      <div className="flex justify-center mb-12">
        <IntenseGlowCard identifier="category-filter">
          <div className="flex flex-wrap gap-4 p-6">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-3 rounded-full text-sm font-semibold transition-all duration-500 transform hover:scale-110 ${
                  selectedCategory === category
                    ? 'bg-gradient-to-r from-laravelRed to-laravelOrange dark:from-purple-500 dark:to-pink-500 text-white shadow-xl scale-105 category-pulse'
                    : 'bg-laravelGray-100 dark:bg-laravelGray-800 text-laravelGray-700 dark:text-laravelGray-300 hover:bg-gradient-to-r hover:from-laravelRed hover:to-laravelOrange dark:hover:from-purple-500 dark:hover:to-pink-500 hover:text-white hover:shadow-lg'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </IntenseGlowCard>
      </div>

      {/* Skills Grid with Enhanced Glow */}
      <div className="grid grid-cols-2 gap-8 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
        {filteredSkills.map((skill, id) => (
          <SubtleGlowCard key={id} identifier={`skill-${id}`}>
            <div className="relative overflow-hidden group skill-card-hover">
              <div className="flex flex-col items-center justify-center p-6 transition-all duration-500 h-36">
                {/* Skill Icon with enhanced effects */}
                <div className="relative mb-4 transition-transform duration-500 h-14 w-14 group-hover:scale-125 group-hover:rotate-12">
                  <div className="absolute inset-0 transition-opacity duration-500 rounded-full opacity-0 bg-gradient-to-r from-laravelRed/20 to-laravelOrange/20 dark:from-purple-500/20 dark:to-pink-500/20 blur-lg group-hover:opacity-100"></div>
                  <Image
                    src={skillsImage(skill)?.src}
                    alt={skill}
                    width={56}
                    height={56}
                    className="relative z-10 object-contain w-full h-full rounded-lg"
                  />
                </div>

                {/* Skill Name with gradient effect */}
                <p className="text-sm font-semibold text-center transition-all text-laravelGray-900 dark:text-white duration-400 group-hover:bg-gradient-to-r group-hover:from-laravelRed group-hover:to-laravelOrange dark:group-hover:from-purple-500 dark:group-hover:to-pink-500 group-hover:bg-clip-text group-hover:text-transparent">
                  {skill}
                </p>

                {/* Category Badge with animation */}
                <span className="absolute px-2 py-1 text-xs transition-all duration-500 transform translate-x-2 rounded-full opacity-0 top-3 right-3 bg-gradient-to-r from-laravelRed/20 to-laravelOrange/20 dark:from-purple-500/20 dark:to-pink-500/20 text-laravelRed dark:text-purple-300 group-hover:opacity-100 group-hover:translate-x-0">
                  {getSkillCategory(skill)}
                </span>

                {/* Animated border effect */}
                <div className="absolute inset-0 transition-opacity duration-500 rounded-lg opacity-0 bg-gradient-to-r from-laravelRed via-laravelOrange to-laravelRed dark:from-purple-500 dark:via-pink-500 dark:to-purple-500 group-hover:opacity-20 animate-pulse"></div>
              </div>
            </div>
          </SubtleGlowCard>
        ))}
      </div>

      {/* Skills Summary with Intense Glow */}
      <div className="grid grid-cols-1 gap-8 mt-20 md:grid-cols-2 lg:grid-cols-4">
        <IntenseGlowCard identifier="backend-summary">
          <div className="p-8 text-center transition-transform duration-500 group hover:scale-105">
            <div className="mb-3 text-4xl font-bold text-transparent transition-transform duration-300 bg-gradient-to-r from-laravelRed to-laravelOrange dark:from-purple-500 dark:to-pink-500 bg-clip-text group-hover:scale-110">
              {skillsData.filter(skill => getSkillCategory(skill) === 'Backend').length}+
            </div>
            <h3 className="mb-3 text-xl font-bold transition-colors duration-300 text-laravelGray-900 dark:text-white group-hover:text-laravelRed dark:group-hover:text-purple-400">Backend</h3>
            <p className="text-sm text-laravelGray-600 dark:text-laravelGray-300">Server-side technologies</p>
            <div className="h-1 mt-4 transition-transform duration-500 transform scale-x-0 rounded-full bg-gradient-to-r from-laravelRed to-laravelOrange dark:from-purple-500 dark:to-pink-500 group-hover:scale-x-100"></div>
          </div>
        </IntenseGlowCard>

        <IntenseGlowCard identifier="frontend-summary">
          <div className="p-8 text-center transition-transform duration-500 group hover:scale-105">
            <div className="mb-3 text-4xl font-bold text-transparent transition-transform duration-300 bg-gradient-to-r from-laravelOrange to-laravelYellow dark:from-pink-500 dark:to-purple-500 bg-clip-text group-hover:scale-110">
              {skillsData.filter(skill => getSkillCategory(skill) === 'Frontend').length}+
            </div>
            <h3 className="mb-3 text-xl font-bold transition-colors duration-300 text-laravelGray-900 dark:text-white group-hover:text-laravelOrange dark:group-hover:text-pink-400">Frontend</h3>
            <p className="text-sm text-laravelGray-600 dark:text-laravelGray-300">Client-side technologies</p>
            <div className="h-1 mt-4 transition-transform duration-500 transform scale-x-0 rounded-full bg-gradient-to-r from-laravelOrange to-laravelYellow dark:from-pink-500 dark:to-purple-500 group-hover:scale-x-100"></div>
          </div>
        </IntenseGlowCard>

        <IntenseGlowCard identifier="database-summary">
          <div className="p-8 text-center transition-transform duration-500 group hover:scale-105">
            <div className="mb-3 text-4xl font-bold text-transparent transition-transform duration-300 bg-gradient-to-r from-laravelYellow to-laravelRed dark:from-blue-500 dark:to-purple-500 bg-clip-text group-hover:scale-110">
              {skillsData.filter(skill => getSkillCategory(skill) === 'Database').length}+
            </div>
            <h3 className="mb-3 text-xl font-bold transition-colors duration-300 text-laravelGray-900 dark:text-white group-hover:text-laravelYellow dark:group-hover:text-blue-400">Database</h3>
            <p className="text-sm text-laravelGray-600 dark:text-laravelGray-300">Data management systems</p>
            <div className="h-1 mt-4 transition-transform duration-500 transform scale-x-0 rounded-full bg-gradient-to-r from-laravelYellow to-laravelRed dark:from-blue-500 dark:to-purple-500 group-hover:scale-x-100"></div>
          </div>
        </IntenseGlowCard>

        <IntenseGlowCard identifier="tools-summary">
          <div className="p-8 text-center transition-transform duration-500 group hover:scale-105">
            <div className="mb-3 text-4xl font-bold text-transparent transition-transform duration-300 bg-gradient-to-r from-laravelRed to-laravelYellow dark:from-purple-500 dark:to-blue-500 bg-clip-text group-hover:scale-110">
              {skillsData.filter(skill => ['Tools', 'Other'].includes(getSkillCategory(skill))).length}+
            </div>
            <h3 className="mb-3 text-xl font-bold transition-colors duration-300 text-laravelGray-900 dark:text-white group-hover:text-laravelRed dark:group-hover:text-purple-400">Tools & Others</h3>
            <p className="text-sm text-laravelGray-600 dark:text-laravelGray-300">Development tools & utilities</p>
            <div className="h-1 mt-4 transition-transform duration-500 transform scale-x-0 rounded-full bg-gradient-to-r from-laravelRed to-laravelYellow dark:from-purple-500 dark:to-blue-500 group-hover:scale-x-100"></div>
          </div>
        </IntenseGlowCard>
      </div>

      {/* Proficiency Levels with Enhanced Glow */}
      <div className="mt-20">
        <div className="mb-12 text-center">
          <IntenseGlowCard identifier="proficiency-header">
            <div className="p-6">
              <h2 className="text-3xl font-bold text-transparent bg-gradient-to-r from-laravelRed to-laravelOrange dark:from-purple-500 dark:to-pink-500 bg-clip-text">
                Proficiency Levels
              </h2>
              <p className="mt-2 text-laravelGray-600 dark:text-laravelGray-300">My expertise across different technologies</p>
            </div>
          </IntenseGlowCard>
        </div>

        <div className="grid grid-cols-1 gap-10 md:grid-cols-2">
          <IntenseGlowCard identifier="expert-skills">
            <div className="p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-4 h-4 mr-3 transition-transform duration-300 rounded-full bg-gradient-to-r from-laravelRed to-laravelOrange group-hover:scale-125"></div>
                <h3 className="text-2xl font-bold transition-transform duration-300 text-laravelRed dark:text-laravelRedLight group-hover:scale-105">Expert Level</h3>
              </div>
              <div className="flex flex-wrap gap-3">
                {['PHP', 'Laravel', 'MySQL', 'HTML', 'CSS', 'Javascript'].map((skill, index) => (
                  <span
                    key={skill}
                    className="px-4 py-2 text-sm font-semibold transition-all duration-300 rounded-full cursor-pointer bg-gradient-to-r from-laravelRed/20 to-laravelOrange/20 dark:from-laravelRed/30 dark:to-laravelOrange/30 text-laravelRed dark:text-laravelRedLight hover:scale-110 hover:shadow-lg"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {skill}
                  </span>
                ))}
              </div>
              <div className="h-2 mt-6 rounded-full bg-gradient-to-r from-laravelRed to-laravelOrange">
                <div className="bg-white/30 h-full rounded-full w-[95%] group-hover:w-full transition-all duration-1000"></div>
              </div>
            </div>
          </IntenseGlowCard>

          <IntenseGlowCard identifier="intermediate-skills">
            <div className="p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-4 h-4 mr-3 transition-transform duration-300 rounded-full bg-gradient-to-r from-laravelOrange to-laravelYellow group-hover:scale-125"></div>
                <h3 className="text-2xl font-bold transition-transform duration-300 text-laravelOrange dark:text-orange-400 group-hover:scale-105">Intermediate Level</h3>
              </div>
              <div className="flex flex-wrap gap-3">
                {['React', 'Vue', 'Node JS', 'Docker', 'AWS'].map((skill, index) => (
                  <span
                    key={skill}
                    className="px-4 py-2 text-sm font-semibold transition-all duration-300 rounded-full cursor-pointer bg-gradient-to-r from-laravelOrange/20 to-laravelYellow/20 dark:from-orange-400/30 dark:to-yellow-400/30 text-laravelOrange dark:text-orange-400 hover:scale-110 hover:shadow-lg"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {skill}
                  </span>
                ))}
              </div>
              <div className="h-2 mt-6 rounded-full bg-gradient-to-r from-laravelOrange to-laravelYellow">
                <div className="bg-white/30 h-full rounded-full w-[75%] group-hover:w-[85%] transition-all duration-1000"></div>
              </div>
            </div>
          </IntenseGlowCard>
        </div>
      </div>
    </div>
  );
};

export default EnhancedSkills;
