'use client';

import { skillsData } from "@/utils/data/skills";
import { skillsImage } from "@/utils/skill-image";
import Image from "next/image";
import { useState } from "react";
import SimpleGlowCard from "../helper/simple-glow-card";

const EnhancedSkills = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  
  // Group skills by category
  const categories = ['All', 'Backend', 'Frontend', 'Database', 'Tools', 'Other'];
  
  const getSkillCategory = (skill) => {
    const backendSkills = ['PHP', 'Laravel', 'Node JS', 'Express JS'];
    const frontendSkills = ['HTML', 'CSS', 'Javascript', 'TypeScript', 'React', 'Next JS', 'Vue', 'Nuxt JS', 'Tailwind'];
    const databaseSkills = ['MySQL', 'PostgreSQL', 'MongoDB'];
    const toolSkills = ['Git', 'Docker', 'AWS', 'Linux', 'Nginx'];
    
    if (backendSkills.includes(skill)) return 'Backend';
    if (frontendSkills.includes(skill)) return 'Frontend';
    if (databaseSkills.includes(skill)) return 'Database';
    if (toolSkills.includes(skill)) return 'Tools';
    return 'Other';
  };

  const filteredSkills = selectedCategory === 'All' 
    ? skillsData 
    : skillsData.filter(skill => getSkillCategory(skill) === selectedCategory);

  return (
    <div className="relative z-50 my-12 lg:my-24">
      {/* Clean background without animations */}

      {/* Header Section with Simple Glow */}
      <div className="flex justify-center my-5 lg:py-8">
        <SimpleGlowCard identifier="skills-header">
          <div className="flex items-center px-8 py-6">
            <span className="w-24 h-[2px] bg-laravelRed dark:bg-purple-500 transition-colors duration-400"></span>
            <span className="p-4 px-8 mx-4 text-2xl font-bold text-white transition-colors rounded-lg bg-laravelRed dark:bg-purple-500 w-fit duration-400">
              Technical Skills
            </span>
            <span className="w-24 h-[2px] bg-laravelRed dark:bg-purple-500 transition-colors duration-400"></span>
          </div>
        </SimpleGlowCard>
      </div>

      {/* Category Filter with Simple Glow */}
      <div className="flex justify-center mb-12">
        <SimpleGlowCard identifier="category-filter">
          <div className="flex flex-wrap gap-4 p-6">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105 ${
                  selectedCategory === category
                    ? 'bg-laravelRed dark:bg-purple-500 text-white shadow-lg scale-105'
                    : 'bg-laravelGray-100 dark:bg-laravelGray-800 text-laravelGray-700 dark:text-laravelGray-300 hover:bg-laravelRed dark:hover:bg-purple-500 hover:text-white hover:shadow-md'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </SimpleGlowCard>
      </div>

      {/* Skills Grid with Simple Glow */}
      <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
        {filteredSkills.map((skill, id) => (
          <SimpleGlowCard key={id} identifier={`skill-${id}`}>
            <div className="relative overflow-hidden group">
              <div className="flex flex-col items-center justify-center h-32 p-6 transition-all duration-300">
                {/* Skill Icon */}
                <div className="w-12 h-12 mb-3 transition-transform duration-300 group-hover:scale-110">
                  <Image
                    src={skillsImage(skill)?.src}
                    alt={skill}
                    width={48}
                    height={48}
                    className="object-contain w-full h-full rounded-lg"
                  />
                </div>

                {/* Skill Name */}
                <p className="text-sm font-medium text-center transition-colors duration-300 text-laravelGray-900 dark:text-white group-hover:text-laravelRed dark:group-hover:text-purple-400">
                  {skill}
                </p>

                {/* Category Badge */}
                <span className="absolute px-2 py-1 text-xs transition-opacity duration-300 rounded-full opacity-0 top-2 right-2 bg-laravelRed/10 dark:bg-purple-500/20 text-laravelRed dark:text-purple-300 group-hover:opacity-100">
                  {getSkillCategory(skill)}
                </span>
              </div>
            </div>
          </SimpleGlowCard>
        ))}
      </div>

      {/* Skills Summary with Simple Glow */}
      <div className="grid grid-cols-1 gap-6 mt-16 md:grid-cols-2 lg:grid-cols-4">
        <SimpleGlowCard identifier="backend-summary">
          <div className="p-6 text-center group">
            <div className="mb-3 text-3xl font-bold text-laravelRed dark:text-purple-400">
              {skillsData.filter(skill => getSkillCategory(skill) === 'Backend').length}+
            </div>
            <h3 className="mb-2 text-lg font-semibold text-laravelGray-900 dark:text-white">Backend</h3>
            <p className="text-sm text-laravelGray-600 dark:text-laravelGray-300">Server-side technologies</p>
          </div>
        </SimpleGlowCard>

        <SimpleGlowCard identifier="frontend-summary">
          <div className="p-6 text-center group">
            <div className="mb-3 text-3xl font-bold text-laravelOrange dark:text-pink-400">
              {skillsData.filter(skill => getSkillCategory(skill) === 'Frontend').length}+
            </div>
            <h3 className="mb-2 text-lg font-semibold text-laravelGray-900 dark:text-white">Frontend</h3>
            <p className="text-sm text-laravelGray-600 dark:text-laravelGray-300">Client-side technologies</p>
          </div>
        </SimpleGlowCard>

        <SimpleGlowCard identifier="database-summary">
          <div className="p-6 text-center group">
            <div className="mb-3 text-3xl font-bold text-laravelYellow dark:text-blue-400">
              {skillsData.filter(skill => getSkillCategory(skill) === 'Database').length}+
            </div>
            <h3 className="mb-2 text-lg font-semibold text-laravelGray-900 dark:text-white">Database</h3>
            <p className="text-sm text-laravelGray-600 dark:text-laravelGray-300">Data management systems</p>
          </div>
        </SimpleGlowCard>

        <SimpleGlowCard identifier="tools-summary">
          <div className="p-6 text-center group">
            <div className="mb-3 text-3xl font-bold text-laravelRed dark:text-purple-400">
              {skillsData.filter(skill => ['Tools', 'Other'].includes(getSkillCategory(skill))).length}+
            </div>
            <h3 className="mb-2 text-lg font-semibold text-laravelGray-900 dark:text-white">Tools & Others</h3>
            <p className="text-sm text-laravelGray-600 dark:text-laravelGray-300">Development tools & utilities</p>
          </div>
        </SimpleGlowCard>
      </div>

      {/* Proficiency Levels with Simple Glow */}
      <div className="mt-16">
        <div className="mb-8 text-center">
          <SimpleGlowCard identifier="proficiency-header">
            <div className="p-6">
              <h2 className="text-2xl font-bold text-laravelRed dark:text-purple-400">
                Proficiency Levels
              </h2>
              <p className="mt-2 text-laravelGray-600 dark:text-laravelGray-300">My expertise across different technologies</p>
            </div>
          </SimpleGlowCard>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <SimpleGlowCard identifier="expert-skills">
            <div className="p-6 group">
              <h3 className="mb-4 text-xl font-semibold text-laravelRed dark:text-purple-400">Expert Level</h3>
              <div className="flex flex-wrap gap-2">
                {['PHP', 'Laravel', 'MySQL', 'HTML', 'CSS', 'Javascript'].map((skill) => (
                  <span
                    key={skill}
                    className="px-3 py-1 text-sm font-medium rounded-full bg-laravelRed/20 dark:bg-purple-500/30 text-laravelRed dark:text-purple-300"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          </SimpleGlowCard>

          <SimpleGlowCard identifier="intermediate-skills">
            <div className="p-6 group">
              <h3 className="mb-4 text-xl font-semibold text-laravelOrange dark:text-orange-400">Intermediate Level</h3>
              <div className="flex flex-wrap gap-2">
                {['React', 'Vue', 'Node JS', 'Docker', 'AWS'].map((skill) => (
                  <span
                    key={skill}
                    className="px-3 py-1 text-sm font-medium rounded-full bg-laravelOrange/20 dark:bg-orange-400/30 text-laravelOrange dark:text-orange-400"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          </SimpleGlowCard>
        </div>
      </div>
    </div>
  );
};

export default EnhancedSkills;
