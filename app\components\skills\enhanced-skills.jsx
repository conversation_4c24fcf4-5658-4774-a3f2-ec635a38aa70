'use client';

import { skillsData } from "@/utils/data/skills";
import { skillsImage } from "@/utils/skill-image";
import Image from "next/image";
import GlowCard from "../helper/glow-card";

const EnhancedSkills = () => {
  const getSkillCategory = (skill) => {
    const backendSkills = ['PHP', 'Laravel', 'Node JS', 'Express JS'];
    const frontendSkills = ['HTML', 'CSS', 'Javascript', 'TypeScript', 'React', 'Next JS', 'Vue', 'Nuxt JS', 'Tailwind'];
    const databaseSkills = ['MySQL', 'PostgreSQL', 'MongoDB'];
    const toolSkills = ['Git', 'Docker', 'AWS', 'Linux', 'Nginx'];

    if (backendSkills.includes(skill)) return 'Backend';
    if (frontendSkills.includes(skill)) return 'Frontend';
    if (databaseSkills.includes(skill)) return 'Database';
    if (toolSkills.includes(skill)) return 'Tools';
    return 'Other';
  };

  return (
    <div className="relative z-50 my-12 lg:my-24">
      {/* Header Section */}
      <div className="flex justify-center my-5 lg:py-8">
        <div className="flex items-center">
          <span className="w-24 h-[2px] bg-laravelRed dark:bg-[#1a1443] transition-colors duration-400"></span>
          <span className="bg-laravelRed dark:bg-[#1a1443] w-fit text-white p-2 px-5 text-xl rounded-md transition-colors duration-400">
            Skills
          </span>
          <span className="w-24 h-[2px] bg-laravelRed dark:bg-[#1a1443] transition-colors duration-400"></span>
        </div>
      </div>

      {/* Skills Grid - Experience Card Style */}
      <div className="py-8">
        <div className="flex flex-col gap-6">
          {skillsData.map((skill, id) => (
            <GlowCard key={id} identifier={`skill-${id}`}>
              <div className="relative p-4">
                <Image
                  src="/blur-23.svg"
                  alt="Background blur"
                  width={1080}
                  height={200}
                  className="absolute bottom-0 opacity-30 dark:opacity-80"
                />
                <div className="flex justify-center mb-3">
                  <p className="text-xs sm:text-sm text-laravelRed dark:text-[#16f2b3] transition-colors duration-400 font-medium">
                    {getSkillCategory(skill)}
                  </p>
                </div>
                <div className="flex items-center px-2 gap-x-6">
                  <div className="flex-shrink-0 transition-all duration-300 text-laravelRed dark:text-violet-500 hover:scale-110">
                    <Image
                      src={skillsImage(skill)?.src}
                      alt={skill}
                      width={36}
                      height={36}
                      className="object-contain rounded-lg h-9 w-9"
                    />
                  </div>
                  <div className="flex-1">
                    <p className="mb-2 text-base font-semibold leading-tight uppercase transition-colors sm:text-xl text-laravelGray-900 dark:text-white duration-400">
                      {skill}
                    </p>
                    <p className="text-sm leading-relaxed transition-colors sm:text-base text-laravelGray-600 dark:text-laravelGray-300 duration-400">
                      Technology
                    </p>
                  </div>
                </div>
              </div>
            </GlowCard>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EnhancedSkills;
