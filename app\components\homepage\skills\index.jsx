// @flow strict

import { skillsData } from "@/utils/data/skills";
import { skillsImage } from "@/utils/skill-image";
import Image from "next/image";
import Marquee from "react-fast-marquee";
import GlowCard from "../../helper/glow-card";

function Skills() {
  return (
    <div id="skills" className="relative z-50 border-t my-12 lg:my-24 border-laravelGray-200 dark:border-[#25213b] transition-colors duration-400">
      <div className="w-[100px] h-[100px] bg-laravelRed/20 dark:bg-[#16f2b3]/20 rounded-full absolute top-6 left-[42%] translate-x-1/2 filter blur-3xl opacity-20 transition-colors duration-400"></div>

      <div className="flex justify-center -translate-y-[1px]">
        <div className="w-3/4">
          <div className="h-[1px] bg-gradient-to-r from-transparent via-laravelRed to-transparent dark:via-[#16f2b3] w-full transition-colors duration-400" />
        </div>
      </div>

      <div className="flex justify-center my-5 lg:py-8">
        <div className="flex items-center">
          <span className="w-24 h-[2px] bg-laravelRed dark:bg-[#1a1443] transition-colors duration-400"></span>
          <span className="bg-laravelRed dark:bg-[#1a1443] w-fit text-white p-2 px-5 text-xl rounded-md transition-colors duration-400">
            Skills
          </span>
          <span className="w-24 h-[2px] bg-laravelRed dark:bg-[#1a1443] transition-colors duration-400"></span>
        </div>
      </div>

      <div className="w-full my-12">
        <Marquee
          gradient={false}
          speed={80}
          pauseOnHover={true}
          pauseOnClick={true}
          delay={0}
          play={true}
          direction="left"
        >
          {skillsData.map((skill, id) => (
            <div className="w-36 min-w-fit h-32 flex flex-col items-center justify-center transition-all duration-500 m-3 sm:m-5 rounded-lg group relative hover:scale-[1.15] cursor-pointer"
              key={id}>
              <GlowCard identifier={`skill-${id}`}>
                <div className="h-full w-full flex flex-col justify-between p-4">
                  <div className="flex flex-col items-center justify-center gap-2">
                    <div className="h-8 w-8 flex-shrink-0">
                      <Image
                        src={skillsImage(skill)?.src}
                        alt={skill}
                        width={32}
                        height={32}
                        className="h-full w-full object-contain rounded-lg"
                      />
                    </div>
                    <p className="text-laravelRed dark:text-laravelRed text-xs font-medium transition-colors duration-400 text-center line-clamp-2">
                      {skill}
                    </p>
                  </div>
                </div>
              </GlowCard>
            </div>
          ))}
        </Marquee>
      </div>
    </div>
  );
};

export default Skills;