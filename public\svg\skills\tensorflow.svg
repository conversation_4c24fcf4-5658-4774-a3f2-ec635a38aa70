<svg width="256" height="287" viewBox="0 0 256 287" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="256" height="287">
<path d="M133.447 0L256 69.7191V133.447L182.468 90.417V122.553L218.962 143.796L219.506 198.809L182.468 177.566V258.179L133.447 286.502V0ZM122.553 0V286.502L73.5319 258.179V90.417L0 133.447V69.7191L122.553 0Z" fill="white"/>
</mask>
<g mask="url(#mask0)">
<path d="M133.447 0L256 69.7191V133.447L182.468 90.417V122.553L218.962 143.796L219.506 198.809L182.468 177.566V258.179L133.447 286.502V0ZM122.553 0V286.502L73.5319 258.179V90.417L0 133.447V69.7191L122.553 0Z" fill="url(#paint0_linear)"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="0" y1="143.251" x2="256" y2="143.251" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6F00"/>
<stop offset="1" stop-color="#FFA800"/>
</linearGradient>
</defs>
</svg>
