import ayla from '/public/image/ayla.jpg';
import crefin from '/public/image/crefin.jpg';
import realEstate from '/public/image/real-estate.jpg';
import travel from '/public/image/travel.jpg';

export const projectsData = [
    {
        id: 1,
        name: '<PERSON><PERSON>',
        description: "A web application for preparing and modifying product invoices for customers, I was responsible for designing and developing the application API and also building the control panel for the system administrator using Laravel framework",
        tools: ['Laravel', 'MySql', 'Vuejs'],
        role: 'Backend Developer',
        code: '',
        demo: '',
        image: '',
    },
    {
        id: 2,
        name: 'Lostzom',
        description: "A web application to add lost items in metro and bus stations of the Saudi government, I was responsible for designing and developing the application API and also building the control panel for the system administrator using Laravel framework",
        tools: ['Lara<PERSON>', 'MySql', 'Vuejs'],
        role: 'Backend Developer',
        code: '',
        demo: '',
        image: '',
    },
    {
        id: 3,
        name: 'Saudi Numbers Platform',
        description: "An electronic platform to provide statistical services through a huge digital database of data, indicators and statistics in various fields of development in the Kingdom of Saudi Arabia",
        tools: ['<PERSON><PERSON>', 'MySql', 'Chartjs'],
        role: 'Backend Developer',
        code: '',
        demo: 'https://saudistats.info',
        image: '',
    },
    {
        id: 4,
        name: 'Dr. Ali Namla',
        description: "Biography website Dr. Ali bin Ibrahim Al-Namla, Minister of Labor and Social Affairs in the Kingdom of Saudi Arabia, through which he can add articles, books and videos of his own, display and edit them, he can also respond to customer complaints and communicate with them.",
        tools: ['Laravel', 'MySql', 'Jquery', 'Ajax'],
        role: 'Backend Developer',
        code: '',
        demo: 'https://dralialnamlah.com/',
        image: '',
    },
    {
        id: 5,
        name: "Al-Luhaidan For Law and Legal Consultations",
        description: "Mohammed bin Abdul Rahman bin Abdullah Al Luhaidan Advocates and Legal Consultants (“Al Luhaidan Company”) is a professional firm licensed to practice law by the Ministry of Justice in the Kingdom of Saudi Arabia.",
        tools: ['Laravel', 'MySql', 'Jquery', 'Ajax'],
        role: 'Backend Developer',
        code: '',
        demo: 'https://llf.com.sa/',
        image: '',
    },
    {
        id: 6,
        name: "Handprint",
        description: "An introductory website for Handprint Advertising Company that displays the company's business, contact information, and an overview of the company",
        tools: ['Laravel', 'MySql', 'Jquery', 'Ajax'],
        role: 'Backend Developer',
        code: '',
        demo: '',
        image: '',
    },
    {
        id: 7,
        name: "Hakem Web",
        description: "Hakem Web website offering a wealth of valuable medical articles. Through this platform, I have curated a comprehensive collection of informative content, covering diverse topics such as disease management, treatment options, and advancements in the medical field",
        tools: ['Laravel', 'MySql', 'Jquery', 'Ajax', 'Firebase Notifications'],
        role: 'Backend Developer',
        code: '',
        demo: 'https://github.com/Kamal-Abouzayed/hakem-web',
        image: '',
    },
    {
        id: 8,
        name: "Retaam",
        description: "Introducing Ratam Company's website – your gateway to advanced ICT solutions. Discover services, products, and an online store. Seamlessly browse, shop, and order. Experience innovation with Ratam Company.",
        tools: ['Laravel', 'MySql', 'Livewire'],
        role: 'Backend Developer',
        code: '',
        demo: '',
        image: '',
    },
];


// Do not remove any property.
// Leave it blank instead as shown below

// {
//     id: 1,
//     name: '',
//     description: "",
//     tools: [],
//     role: '',
//     code: '',
//     demo: '',
//     image: crefin,
// },
