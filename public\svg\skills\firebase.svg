<svg width="256" height="351" viewBox="0 0 256 351" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 282.998L2.12251 280.026L102.527 89.5119L102.739 87.4951L58.4788 4.35812C54.7706 -2.6061 44.3313 -0.845305 43.1143 6.95059L0 282.998Z" fill="#FFC24A"/>
<path d="M1.25195 280.732L2.85756 277.601L102.21 89.0837L58.0605 5.60859C54.3912 -1.28266 45.0733 0.474059 43.8691 8.18828L1.25195 280.732Z" fill="#FFA712"/>
<g filter="url(#filter0_i)">
<path d="M1.25195 280.732L2.85756 277.601L102.21 89.0837L58.0605 5.60859C54.3912 -1.28266 45.0733 0.474059 43.8691 8.18828L1.25195 280.732Z" fill="#FCA20E"/>
</g>
<path d="M135.006 150.381L167.961 116.63L134.996 53.6996C131.867 47.7428 123.13 47.7256 120.033 53.6996L102.422 87.2884V90.1491L135.006 150.381Z" fill="#F4BD62"/>
<path d="M134.418 148.974L166.457 116.161L134.418 55.1542C131.376 49.3631 123.985 48.7564 120.975 54.5642L103.27 88.6734L102.74 90.4171L134.418 148.974Z" fill="#FFA50E"/>
<g filter="url(#filter1_i)">
<path d="M134.418 148.974L166.457 116.161L134.418 55.1542C131.376 49.3631 123.985 48.7564 120.975 54.5642L103.27 88.6734L102.74 90.4171L134.418 148.974Z" fill="#FCA20E"/>
</g>
<path d="M0 282.997L0.962097 282.03L4.45771 280.609L132.935 152.609L134.563 148.178L102.513 87.1045L0 282.997Z" fill="#F6820C"/>
<path d="M139.121 347.551L255.396 282.703L222.191 78.209C221.153 71.8109 213.303 69.2815 208.724 73.8691L0 282.998L115.608 347.545C122.914 351.625 131.813 351.627 139.121 347.551Z" fill="#FDE068"/>
<path d="M254.354 282.16L221.402 79.2184C220.371 72.8688 213.843 70.2414 209.299 74.7942L1.28906 282.601L115.627 346.51C122.878 350.558 131.709 350.56 138.961 346.516L254.354 282.16Z" fill="#FCCA3F"/>
<path d="M139.121 345.641C131.813 349.717 122.914 349.715 115.608 345.635L0.931157 282.015L0 282.998L115.608 347.546C122.914 351.625 131.813 351.627 139.121 347.552L255.396 282.704L255.111 280.952L139.121 345.641Z" fill="#EEAB37"/>
<defs>
<filter id="filter0_i" x="1.25195" y="1.30176" width="100.958" height="279.43" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="17.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter1_i" x="102.74" y="43.498" width="64.7163" height="105.476" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="-9"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
</defs>
</svg>
