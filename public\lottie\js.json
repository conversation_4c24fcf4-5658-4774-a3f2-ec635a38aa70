{"v": "5.5.7", "meta": {"g": "LottieFiles AE 0.1.20", "a": "", "k": "", "d": "", "tc": ""}, "fr": 29.9700012207031, "ip": 0, "op": 30.0000012219251, "w": 1920, "h": 1200, "nm": "Animation 3", "ddd": 1, "assets": [{"id": "image_0", "w": 898, "h": 898, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA4IAAAOCCAYAAAAiCWxhAAAgAElEQVR4Xuy9T8gex7fndw4E8pexTRImEIJlGJhZBCyHWWRxQfIui4DlzCI3K8urhFyC5VWSxcUyzCKLgOUwCclmLK1mk2CJZDGziSWYWWQx2ArcMASCJZLFvRDGMmQxCQMnHLke/x69et6uU91V3VXdnwbxM7+3uurU51T3U9+uqnNUuCAAAQhAAAIQgAAEIAABCEDgUAT0UL2lsxCAAAQgAAEIQAACEIAABCAgCEEGAQQgAAEIQAACEIAABCAAgYMRQAgezOF0FwIQgAAEIAABCEAAAhCAAEKQMQABCEAAAhCAAAQgAAEIQOBgBBCCB3M43YUABCAAAQhAAAIQgAAEIIAQZAxAAAIQgAAEIAABCEAAAhA4GAGE4MEcTnchAAEIQAACEIAABCAAAQggBBkDEIAABCAAAQhAAAIQgAAEDkYAIXgwh9NdCEAAAhCAAAQgAAEIQAACCEHGAAQgAAEIQAACEIAABCAAgYMRQAgezOF0FwIQgAAEIAABCEAAAhCAAEKQMQABCEAAAhCAAAQgAAEIQOBgBBCCB3M43YUABCAAAQhAAAIQgAAEIIAQZAxAAAIQgAAEIAABCEAAAhA4GAGE4MEcTnchAAEIQAACEIAABCAAAQggBBkDEIAABCAAgUEJmNlNEbktIu+m/z3vyQsR8X8/ichTVX01aDcxGwIQgAAEGhBACDaASpUQgAAEIACBVgSS+LsnIndE5J2Cdp6LyAMReYwoLKBGUQhAAAI7JYAQ3Klj6RYEIAABCOyLgJn5yt99Ebm1sGe/uiBUVa+LCwIQgAAEDkoAIXhQx9NtCEAAAhAYg4CZ+bZPF21fVLbYVwjvqqpvHeWCAAQgAIGDEUAIHszhdBcCEIAABMYhkLaBPhSRDxta/bmqehtcEIAABCBwIAIIwQM5m65CAAIQgMA4BJIIfFp4DnBuBxGDc8lxHwQgAIFBCSAEB3UcZkMAAhCAwH4JrCwCTyARg/sdUvQMAhCAwFsEEIIMCghAAAIQgEBHBNKZQE/7UBIRtFYPPlZVX4XkggAEIACBnRNACO7cwXQPAhCAAATGImBmLsRKIoO+9DyBKV/geeAXjzLq/0rq8oiiN0gvMdaYwVoIQAACcwggBOdQ4x4IQAACEIBAAwJmdldEvgtW/cyjieZW8MzsRoo6+lmw3keq6nZwQQACEIDAjgkgBHfsXLoGAQhAAALjECjcEvqlqnpy+PCV8hA+Dm45/UBVfXsqFwQgAAEI7JQAQnCnjqVbEIAABCAwFgEzuyci3wSs/lRVXdAVX0ls+vbR9zM3sypYTJcbIAABCIxFACE4lr+wFgIQgAAEdkrAzHwFLifQFkf2LIhI+h5nBXc62OgWBCAAARFBCDIMIAABCEAAAhsTSOLsx4wZT1T1Tg1Tg6uPi0VnDVupAwIQgAAE2hBACLbhSq0QgAAEIACBMAEzuy8iX2VuqLpCF1iBrCY8wyAoCAEIQAACqxFACK6GmoYgAAEIQAAClwkEUkZUP7MXWBX8VVXfxWcQgAAEILBPAgjBffqVXkEAAhCAwEAEzOxVJprn7AAx12FIaSV+zmAieuhA4whTIQABCJQQQAiW0KIsBCAAAQhAoAEBM7NMtVW3hZ7aCmwP/TiXp7ABDqqEAAQgAIEVCCAEV4BMExCAAAQgAIElK3Oq2uT3OrAlFSHI0IUABCCwUwJNflh2yopuQQACEIAABKoTSInef5io+Jmq3q7esIggBFtQpU4IQAACYxBACI7hJ6yEAAQgAIGdEoic1WNFcKfOp1sQgAAENiSAENwQPk1DAAIQgAAEnEDujGBDIZhLYs/WUIYoBCAAgZ0SQAju1LF0CwIQgAAExiFA1NBxfIWlEIAABPZCACG4F0/SDwhAAAIQGJZA4KweeQSH9S6GQwACEOiTAEKwT79gFQQgAAEIHIiAmd0Xka8muvyriNxQVc83WOUKpI54oqp3qjRGJRCAAAQg0B0BhGB3LsEgCEAAAhA4GgEzuykiP2b6XW1V0Mzuicg3mfY+V9WHR/MF/YUABCBwFAIIwaN4mn5CAAIQgEDXBAIrdG7/YnGWROdTEXknA6RJEvuunYBxEIAABA5EACF4IGfTVQhAAAIQ6JdAcJXOt4jeVtWf5vSkQARWW32cYyf3QAACEIBAewIIwfaMaQECEIAABCCQJWBm74qIp3PIrdR5XV+q6oNspWcFzMzP+/lWz0j9H6iq28IFAQhAAAI7JYAQ3Klj6RYEILBPAmZ221eEPHBI+nfeUZ+4+7+nqupb/7gGIxBcFTz16pmI3M/5Oq0CejCaT4I4vlVVP0PIBQEIQAACOyaAENyxc+kaBCCwDwJm5qLPJ/K+ohNZzfGO+xbCx0kosLIz0FAIpJK42pu/EJG/LyL/s4j8b+mPvrp4+mjwYUH3X4rIzZrRSQvapigEIAABCKxIACG4ImyaggAEIFBC4EwAflZy34WyjxCECwmueHvhFtHaln009/xhbUOoDwIQgAAE2hJACLblS+0QgAAEZhEws7si4mfAoiuAuXZ8hfAe6QBymPr4e0FQl5oGL45IWtMY6oIABCAAgbYEEIJt+VI7BCAAgWICZuYC8IviG2M3cP4rxmnzUiuLQUTg5h7HAAhAAALrEkAIrsub1iAAAQhMEjAzj+q4dCtojjKpAXKEOvl7EoN+1vP9Rib5SvFdVfU2uCAAAQhA4EAEEIIHcjZdhQAE+iZgZh4Q5quVrPxaVb09rs4JpDODvkpc+wOBRx11EUgwoc7HAOZBAAIQaEEAIdiCKnVCAAIQKCSQ0kL8UHjb0uIf51IPLG2A++sRSGPExfuthbV6ZFBPO+Grz1wQgAAEIHBQAgjBgzqebkMAAn0RMDNflYlu//OJvG/l81yBr1JPTukCPMVEuB5V9dQUXAMRWPDR4IknlGcb6EDOxlQIQAACDQkgBBvCpWoIQAACEQIFW0JDkT8LI46yRTTipM7KmJllTPJtnz+lDwVPWfntzIGYAwEIQKADAgjBDpyACRCAwLEJBFcDn3uC8Gii73SuzFcMc8nEX7IqON74ywlBVeX3fTy3YjEEIACBVQnwQ7EqbhqDAAQg8CYBM/OtnN9nuBSJwFNdBWLwU7YLjjUyEYJj+QtrIQABCPRIACHYo1ewCQIQOAyBYLqIj1TVt/kVXyn9wI+ZG0knUUx22xsQgtvyp3UIQAACeyCAENyDF+kDBCAwLIHAttDFIi0gNtkeOtgIQggO5jDMhQAEINAhAYRgh07BJAhA4DgEchN6EZm9GniiGFkV5EzZWGMuN27w51j+xFoIQAACWxBACG5BnTYhAAEIiEggDcCvquppIRZfZuZpJt6ZqIicgospr1cBQnA91rQEAQhAYK8EEIJ79Sz9ggAEuicQEILPVPV2jY6YmUcQnUpEjhCsAXqlOhCCK4GmGQhAAAI7JoAQ3LFz6RoEINA3AYRg3/7p2TqEYM/ewTYIQAACYxBACI7hJ6yEAAR2SCAgBKsFcQkEpWFFcKAxhhAcyFmYCgEIQKBTAgjBTh2DWRCAwP4JpDx/v2R6+oGqvlhCw8xuiMjPmTreiyarX2IL99YhgBCsw5FaIAABCByZAELwyN6n7xCAwOYEAkFcvlTVB0sMNbN7IvLNRB3VgtIssZN74wQQgnFWlIQABCAAgcsEEIKMDAhAAAIbEjCzxyLyyYQJL0Xk5tzVurTq6Mno359o44mq3tkQA00XEkAIFgKjOAQgAAEIvEUAIciggAAEILAhATO7KyLfZUyYnVQ+kEzem/5cVR9uiIGmCwkgBAuBURwCEIAABBCCjAEIQAACPREInhN0k79VVd/iGb6CItDr43xgmGofBRGCffgBKyAAAQiMTIAVwZG9h+0QgMAuCBQIticicje3TTSJS1/hm9pyemI3e7VxF/AH7QRCcFDHYTYEIACBjgggBDtyBqZAAALHJJCievo5vncCBH4VERd5j1XVk8T/fqV0FH7Wz7ebRuvy84eLopIGbKZIZQIIwcpAqQ4CEIDAAQkgBA/odLoMAQj0R8DM7ovIVytb9rWqertcgxFACA7mMMyFAAQg0CEBhGCHTsEkCEDgmATMzFf4bq3U+2eqenultmimMgGEYGWgVAcBCEDggAQQggd0Ol2GAAT6JJDO9rkY/LCxhc9F5HburGFjG3Zbfdqie+rfK1X1bb9VL4RgVZxUBgEIQOCQBBCCh3Q7nYYABHolsIIYRARWdL6Z3RQRP5fpq6tTq7meD9JFvv/z852vlpiBEFxCj3shAAEIQMAJIAQZBxCAAAQ6I1AY9bPE+lDU0ZIKj1o25X/0dB5zV28ficj9uYF6EIJHHXn0GwIQgEA9AgjBeiypCQIQgEBVAklsPAhGAJ1q2yON3iNp/HL3mJmv/rlP3l9e2+saXBC6b4pWCBGClehTDQQgAIEDE0AIHtj5dB0CEOifQFod9JUnTwlRKj58O6KnmnhQKjT6J7OuhckPLgA/a9CyC3XPD/k4WjdCMEqKchCAAAQgcB0BhCBjAwIQgMAgBNJqlJ9F83Npueiin5YIi0EQbGLmCuc2T/0Kp/NACG4yFGgUAhCAwK4IIAR35U46AwEIHIUAQmAdT5vZDRHxqJ/vrNOiPFJVX/2dvPB/jhB/hwAEIACBHAGEYI4Qf4cABCDQIQGEQHunrLgSeLUz2ZVB/N/e/7QAAQhAYO8EEIJ79zD9gwAEdkkAIdDerWbmqR5yW3AvGeJnM1+kP8y532+d3NqL/9v7nxYgAAEI7J0AQnDvHqZ/EIDALgkgBNq61cw8QM83Ba14ag4PzPP0amCelGvQz3Z6ndGAPx5A5sZ1QX7wf4FnKAoBCEAAAhcJIAQZGBCAAAQGJIAQaOe0tCXUV/Qi5wKfpfQPfo4weyWBeT9Y97XnBfF/FjUFIAABCEAgQwAhyBCBAAQgMCABhEA7p5mZC7WvAi18q6q+yld0pRVCXz2MJKP/4FLSefxfhJzCEIAABCBwgQBCkGEBAQhAYEACCIE2TitYDcwGdJmysCAQzUWxif/b+J9aIQABCByJAELwSN6mrxCAwG4IIATauNLMPHXDd5nan6jqnaUWpJVBD0gztQX1V1V992pb+H8pfe6HAAQgAAGEIGMAAhCAwIAEEAJtnGZmj0Xkk4naJ4O4lFoV3Ib6VgRR/F9KmvIQgAAEIHCVAEKQMQEBCEBgQAIIgTZOy3EVkUVbQi9ZbWavMquCb7WZs1NV+X1vM0SoFQIQgMBuCPBDsRtX0hEIQOBIBBAC9b2dtmr+mKn5YvCWJdaY2QMR+WKijmeq6uknfr/w/xLi3AsBCEAAAk4AIcg4gAAEIDAgAYRAfaeZmYutHyZqfq6qN2u3HGj3pareQAjWJk99EIAABI5NACF4bP/TewhAYFACCMH6jgskka8SJOaq5SmC6C9TPbq61RP/1/c/NUIAAhA4GgGE4NE8Tn8hAIFdEEAI1HdjIHBL9fOBp16U+jNXXkTeU1U/e8gFAQhAAAIQuEgAIcjAgAAEIDAggZwQIFhIuVMDQvCRqnp6iepXqT9z5UXkpYjcVVVPT8EFAQhAAAIQeIsAQpBBAQEIQGBAAjkhgBAsd6qZeW7A7yfufCtoS3krb98ROCP4Vi7BnP/PWnmSBCGrgzWcRR0QgAAEdkQAIbgjZ9IVCEDgOARyQgAhWD4WAoJMWnANnE0sjhp6pfee+9BXBz1HIhcEIAABCEDgNQGEIAMBAhCAwIAEEIJtnJbjKiKfq+rDmq2b2U8i8uFEnW9tSQ0kvr9UHauDNR1HXRCAAAQGJ4AQHNyBmA8BCByTQE6wtFi5OgLpgCh7K5XDEi6RVchL4jPlPPTzf+8Utr+L1cHEzdN9vCsip5QeLqh9C+xTzkYWjgqKQwAChySAEDyk2+k0BCAwOgGEYBsPBpK7e8NfqqongV90pbQRLuamVgO9jYsRQJMYdDtuzTBkuNVBM/Ncivd8m2tAALvg9a2w91X1xQw+3AIBCEBg9wQQgrt3MR2EAAT2SAAh2MarSWz8HKj9I1X1FajZl5n5FtPPMhVkcxemM4b3A+LoalPDrA6miK4uAktXQL3PzdJ+zHY+N0IAAhDogABCsAMnYAIEIACBUgIIwVJi8fJm5qt0uVU2F1G354rBoAh0oz+ObHNMAtaFZc7uSyB8dfBejytnadXUV/bm9Ou8r8+Tv4ieGn8UKAkBCOycAEJw5w6mexCAwD4JIATb+TV4bs8NcDHoWw/D20QLBVtxuoqFq4NFfWnngd9qLtg6GzUFMRglRTkIQOAQBBCCh3AznYQABPZGACHY1qOFUTldYDyYiiaaBKCfbSvZ3hhaDbxKolBsXr39WUo1sfm5uuDKbOlAeK6qp+AypfdSHgIQgMCuCCAEd+VOOgMBCByFAEKwrafTapSLoZIzab5C6NtK/1xE/lqKYPmPReTfCgSEudqhb1XVRePsa+TVwXQm8KvZnZ++kTODjcBSLQQgMBYBhOBY/sJaCEAAAq8JIATbD4SCLaK1jam2arVwdXATwTRThJf64IMez0SWdoLyEIAABJYQQAguoce9EIAABDYigBBcB7yZ+XbO79Zp7XUrTc6xLVgdvJi6oiWPgtVAX4H1ADm+CuuX5xW8IyLvB+x7pKruWy4IQAAChyWAEDys6+k4BCAwMgGE4HreW1EMNhGBJ1Jppc2F0ycF9GadUyyo/62iZuZbcnNi7lGKdPpGFNDURw/ek0vL8auqejJ6LghAAAKHJYAQPKzr6TgEIDAyAYTgut5L20Q9jUHJmcESI1dL8G5mvmrmgjDSl1WFoJl5IJcfM+Cyq3nB9Byr9q1kMFAWAhCAwBoEEIJrUKYNCEAAApUJIAQrAw1UN3NFLVfzJkndC/qyqlgKrL46rxuqOpkPMHjO8MuS1B85R/J3CEAAAqMRQAiO5jHshQAEIECwmE3HQFodvL8wybkLGt/C6GknNktyHkjRsLYQdK5T0UKzq4GnwWFmzveLicGySTCcTQcvjUMAAhA4I4AQZDhAAAIQGJAAK4LbOy1tY/SAI9EAJW60bwH1LaaPtxSAZ2LJA63cmqDZmxAMi7dA0JlwXduPNiyAAAQgUJ8AQrA+U2qEAAQg0JwAQrA54qIGzOyPReTvTNxULSVEkWGZwgOuCIbzKyIEa44U6oIABPZIACG4R6/SJwhAYPcEEIJ9uTiQc/CZqnp6g66uDoXgPRH5ZgLSS1W9EYFoZj+JyIcTZVkRjICkDAQgsFsCCMHdupaOQQACeyaAEOzLuwjBOv4IcPSGskFeUmTU7zNWfaqqvk2XCwIQgMAhCSAED+l2Og0BCIxOACHYlwcDAoYVwaDLcmNbRDzQzh1VPSWSf6PmdHbT/5ZLj/FeD+c0g1goBgEIQKA6AYRgdaRUCAEIQKA9gdxkWVV5v7d3w+8tIATrwQ7mAPQGPam8R131LaByFrxnKlLoydAnqupBfrggAAEIHJYAE4XDup6OQwACIxNACPblPYRgPX8EWNZobNVoqDUMpg4IQAACtQkgBGsTpT4IQAACKxBACK4AuaCJgHhha2gZTz+790nBLSVFu/RFSQcoCwEIQKAGAYRgDYrUAQEIQGBlAgjBlYFnmkMI1vWHmb0rIi8C5/xKG/bzhTdV1evmggAEIHBoAgjBQ7ufzkMAAqMSQAj25TmEYH1/pDN/P1aumUihlYFSHQQgMC4BhOC4vsNyCEDgwAQQgn05HyHYxh8pDcTDSiuDn6uq18UFAQhAAAIighBkGEAAArMJpO1bniT7poh4kudTomcP3f5KRJ6eIvrNboQbLxJACPY1MBCC7fyRVgZdwE0lh58y4GVKN/E6uigXBCAAAQj8RgAhyEiAAASKCZiZC777IvJZ4GafhHmI9weBshQJEkAIBkGtVAwh2B60md0TEf/3frA1Pw/o7x5/V3FBAAIQgMAVAghBhgQEIBAmkFYAfVIVydN1tV4XhHevSwIdNoKCrwkgBPsaCAjB9fyRtov+tyLylyda/VNV/ZvrWUVLEIAABMYjgBAcz2dYDIFNCFTYnnWy+2u+0C93IUJwOcOaNSAEa9LM12Vmvv381kRJ8gTmMVICAhA4OAGE4MEHAN2HQIRAEoE+8XonUj5Q5pGq3g2Uo8g1BBCCfQ0NhOC6/kAIrsub1iAAgX0SQAju06/0CgLVCKTtoC4C5wZquM6WLzk3ON9NCMH57FrciRBsQfX6OhGC6/KmNQhAYJ8EEIL79Cu9gkA1AmbmQV7mnAmM2PABiZ0jmN4ugxCcx63VXQjBVmQv14sQXJc3rUEAAvskgBDcp1/pFQSqEEjRQX+uUtnlSp6o6p2G9e+26hZCMG0Bdn94ShCPDHuKzvg8pQN5LCKPEe8Xhbkz+2FiwD1TVS/T1TWqoBrV7q6cjzEQgMDhCSAEDz8EAACB6wmYmefuiqaI8LKep+vdJCQi93njrArOGIQ1haCZ+XlNjwYbDcv/zMsTAfYPjmNFcMYgXnALQnABPG6FAAQgkAggBBkKEIDAtQTMzJPC5wLEXIwCmlYTfQUpd7aQKKIzxmANIVghEuyTlBLEx8mhL4Tguu5HCK7Lm9YgAIF9EkAI7tOv9AoCiwkEJrbexqSIS4FmfJVwaqWpyy1ziwE2rmCpEEy52HwVNyf0cz3xbaO3VfXQYjDwvHQ5zkcVVKPanXuY+DsEIACBNQkgBNekTVsQGIiAmd0TkW8mTH6pqn6ObPJK2w6/myqkqryLciCv/H2JEIz4pNCcw4tBhGDhiFlYHCG4ECC3QwACEBARJl8MAwhA4CIBM/MzY19N4PlWVV0s5oSgnxn8BSGYI1X297lCsEFOyJPhXa54lVGdXxohOJ/dnDsRgnOocQ8EIACBNwkgBBkREIDAXCEYPts3V7TgmusJzGVqZr5VN3ducy768JiY20Cv9yEE1/UMQnBd3rQGAQjskwBCcJ9+pVcQWEyAFcHFCJtWMEcINtgSerWPv3raiSOeF0QINh3ub1WOEFyXN61BAAL7JIAQ3Kdf6RUEFhMInBEMTfoj4oMzguXumikEXwRTRDzyfIEi8vQsHYinmLgVsPRLVX0QKLerIgjBdd2JEFyXN61BAAL7JIAQ3Kdf6RUEFhNIZ8l+zFT0SFVdIFy8UgoJ34o4FZny0GfL5jqqVAgG/eni/s51+QEDq8TeneeqenNuv0a9DyG4rucQguvypjUIQGCfBBCC+/QrvYJAFQLBPIK+enTv6nbANDH29AS5JOWHXEFa6qAZQjAX/MdN+jiXJN7M3KefZex/72jbQxGCS0d02f0IwTJelIYABCBwiQBCkHEBAQhcS8DMfIvfFwFEvpLkWwl966Fft4PbCL3sB6p6ui/QFEWcwAwh6P75ZILe5Oru6b6UG3IyCmxEUO7NiwjBdT2KEFyXN61BAAL7JIAQ3Kdf6RUEqhBIWzt/rlLZ5UpC4qNh+8NWPUMI+nm/qTN+n6qqi8XsxST8bUQIweywqVqAMVgVJ5VBAAIHJYAQPKjj6TYEogSC58Ki1Z2X81XEm6wGzkE3a0UwJwSz20JPljIJRwjOG7X17mIM1mNJTRCAwHEJIASP63t6DoEwgUa55z5XVT9vxjWDQIMVwbA/AuMhLCpndL3LW1gRXNctCMF1edMaBCCwTwIIwX36lV5BoCqBdC7MV5RqJSIPi46qHdlRZTOEYC7ISyh6a3C7MELw7bEW4rv2EB1VUI1q99r+pT0IQAACUwQQgowPCEAgRKCiGEQEhohPF5ohBO+JyDeZprO+CUzA5Yh5IVkRrDCoC6oIjMPDfYwowEdRCEAAAq8JIAQZCBCAQBGBdGbQRcVUbsBLdT4Xkbuq6nkFuRYSmCEEb4hILvCPn9v0VCBvbdlNHwI8imwudcQTVb2zsHvD3Y4QXNdlCMF1edMaBCCwTwIIwX36lV5BoCmBtD3wvxSRvxFo6JmIPOQ8YIBUQZFSIehVB872nSxwn/2vIvKXROT/EZF/QUT+vaD4z64qFnRzmKIIwXVdhRBclzetQQAC+ySAENynX+kVBJoTCEx8/0xE/uhoicWbg08NzBSCd0Xku4Y2vlRVX3k83BV4HjgjWHFUIAQrwqQqCEDgsAQQgod1PR2HwDICo058l/W6n7vnCEG3PjCBXtLJcC7CJY30eO+oz0NgPHR51m5Uu3scu9gEAQgclwBC8Li+p+cQWERg1Invok53dPMCIegrdn5Os/SMZ673j1TVVxwPeY36PIwqqEa1+5APB52GAAS6JYAQ7NY1GAaBvgmMOvHtm2rcurlCMK0K3hQRTwdSSwx6IKDbR94GPOrzMKqgGtXu+BNOSQhAAALtCSAE2zOmBQjsksCoE9+9OGOJEKwsBp+kaLCv9sJ2Tj9GfR5GFVSj2j1nbHEPBCAAgVYEEIKtyFIvBHZOYNSJ717cslQIJjHo20Q9VcStGVw81cR9VfWUEoe/Rn0eRhVUo9p9+AcFABCAQFcEEIJduQNjIDAOgVEnvuMQnra0hhA8tZB8eT8oCF0Aunh8oKov9sJzaT9GfR5GFVSj2r10nHE/BCAAgZoEEII1aVIXBA5EYNSJ715cVFMIngnCPxaRvzPB6M9U9d/cC8Oa/Rj1eRhVUI1qd80xR10QgAAElhJACC4lyP0QOCiBUSe+e3FXIyF4W0R+mGDUZS68Hnw66vMwqqAa1e4exio2QAACEDgRQAgyFiAAgVkEzMwjT/44cfNhk4vPAlp4E0KwEFjj4gjBxoCvVI8QXJc3rUEAAvskgBDcp1/pFQRWIdBCjKxi+A4aacF+VDHTgztHZTeqoBrV7h7GKjZAAAIQYEWQMQABCCwm0EKMLDbqIBW0YD+qmOnB5aOyG1VQjWp3D2MVGyAAAQggBBkDEIDAYgItxMhiow5SQQv2o4qZHlw+KrtRBdWodvcwVrEBAhCAAEKQMQABCCwm0EKMLDbqIBW0YD+qmNmYu8cAACAASURBVOnB5aOyG1VQjWp3D2MVGyAAAQggBBkDEIDAYgItxMhiow5SQQv2o4qZHlw+KrtRBdWodvcwVrEBAhCAAEKQMQABCCwm0EKMLDbqIBW0YD+qmOnB5aOyG1VQjWp3D2MVGyAAAQggBBkDEIDAYgItxMhiow5SQQv2o4qZHlxuZg9F5LMJW7rMwTiqoBrV7h7GKjZAAAIQQAgyBiAAgcUEWoiRxUYdpIIW7BGC8wZPQAR6xQjBeXgv3oUQrAiTqiAAgcMSII/gYV1PxyGwnEALMbLcqmPU0II9QrB87ARFIEKwHO3kHQjBykCpDgIQOCQBhOAh3U6nIVCHQAsxUsey/dfSgj1CsGzcFIhAr/hrVb1f1kL70qMKqlHtbu9RWoAABCAQJ4AQjLOiJAQgcIVACzEC5BiBFuwRgjH2XqpQBD4Xkduq+irewjolRxVUW9idno/b7ssr3nG//iQij1XV/5cLAhCAwBAEEIJDuAkjIdAngRZipM+e9mdVC/YIwZif9yICk6B9KiK3Jnr+sap6ma6utYSgmb0rIvdE5K6IvB+A8FJE7quqBw/iggAEINA1AYRg1+7BOAj0TaCFGOm7x/1Y14I9QjDv3z2JQITgtL/T8+CCLiIAr1bmgvBujyI6P8opAQEIHIUAQvAonqafEGhAoIUYaWDmLqtswR4hmBUGuRQR5xV0ux303Mi1VtZqP4St7TYzXwH8roLdn7M6WIEiVUAAAk0IIASbYKVSCByDQAsxcgxyy3vZgj1C8Hq/7G0l8NTT1oJq+Ui/XENLuyuKwJPxiMFWA4F6IQCBRQQQgovwcTMEjk2ghRg5NtF471uwRwheKzp2txKIELzW13dE5Pv4kxgu2eVZy7D1FIQABHZJACG4S7fSKQisQ6CFGFnH8vFbacEeIfj2uNjrSiBC8KKvPTDMCxF5p8Ebws8M3uwxcmyDvlIlBCAwCAGE4CCOwkwI9EighRjpsZ892tSCPULwTU/vXQR6b1tusWz53LSw28w8z+NXDe3uMpdkw/5SNQQg0DkBhGDnDsI8CPRMoIUY6bm/PdnWgj1C8A8ePoIIRAi+Jfw9H2BkNfCR5wwUES/vq4i+nfSzwPvhpareCJSjCAQgAIFVCCAEV8FMIxDYJ4GcGBGRj0iw3Mb3OfaqWvx+Rwj+5qujiECE4BvCP3I20CPBekqIt5LGm9lNEfF8izkhyVnBNq9EaoUABGYQKJ4ozGiDWyAAgZ0SyIkREflVRG4jBusPgBx7hOA85kcSgQjBN4TgAxH5YmLU+LvMz/j5GcKLVxKDP2ZGHttD5z2a3AUBCDQggBBsAJUqIXAUAjkxkjj4BOoOiZXrjooce4RgOe+jiUCE4BtC0Ffzbk2Mmm9V9V5uVAXG0BNV9dVHLghAAAKbE0AIbu4CDIDAuATMzM/JfBLsAbm0gqAixRCCEUrxMoEJ/HllQySLj/S+RdCVSLtLy9S2u1Z9ZpbbYvpMVW8v7T/3QwACEKhBACFYgyJ1QOCgBAJnyq6SQQxWGisIwUogD3Ym8Cq1WgKonjdiNdW2u1Z9gXciQjDmYkpBAAIrEEAIrgCZJiCwZwJmdldEvivoI2KwANZ1RRGCFSAeXAQ6wVoCqI43YrUE3zlFQVkCHD5VVd8BMXkFbEMI5iDydwhAYDUCCMHVUNMQBPZLIDD5udp5xODC4YAQXAgQEfgaYEAAFQmq5V5ZLLROFRTZHdjmHhJwAZ6PVNU/nnFBAAIQ2JwAQnBzF2AABPZBIJ2NeRgIn37qMGJwgesRggvgIQJ/hxcQLkWCaplXqolAr6jI7mAy+cl3lpl5MJlvMgy+VFWPUMoFAQhAYHMCCMHNXYABENgPgYJcWojBhW5HCM4DaGaeANy3+E1FiDyvfDeBYS4RG0UIFu46KE7cHkz94Ai/FpEHqurJ5F9faUzdz6SfOBX/YCoFxbxRzV0QgAAE5hFACM7jxl0QgMA1BGaIQfJqzRhNCMEZ0H6btOfyxR1GBCYRk0ubULSyNs8r03cFV+tOlczOXWpmnij+w0AfvA3n5uU9Aqgnk88lkvdqQ9tLA+1TBAIQgEAVAgjBKhipBAIQOCcwQwxybqZwCCEEC4H9JgJviMjPwTsfici985Wf4H1DFet9RbAwrcdsEZhEcS71w1Lfbi6ql3aA+yEAgX0RQAjuy5/0BgLdEEAMtnUFQrCcbyC0/6nSw3yY6FkIrikCT44P8CgfeL/dQSL5ueS4DwIQaEYAIdgMLRVDAAKIwXZjACFYzjYoBA8jAtMqWJdbQ7cQgYmHnyF9EdzqGR2Euz5nGoVAOQhAoD8CCMH+fIJFENgVAcRgG3ciBMu5BoTgc1X1816HuQIrYKtvZ9xKBJ6cPuOdNTVeFm1XPcxApKMQgMAmBBCCm2CnUQgci0A6m+WRGiOBGByOf0H/PSrfTmj5KsNDVfUVmMUXQrAcYUAIHi6YR29CsFAE+nvirqp60JaqVxKDng4n+s661D4rgVW9QmUQgEBtAgjB2kSpDwIQuEgghVh3EbRkYrUHulXyJyIEy4cCQvBtZj0Jwd4iuqZ3lucG/KpwtPkqoKeY8JQSXBCAAAS6JYAQ7NY1GAaB/RFADP7u08W5xBCC5c8HQrBfIZjeDb8EvbrqSlva0XDXVx9F5P2Mjc9E5M7eo80G/UQxCECgcwIIwc4dhHkQ2BsBxOBrjy5eFUQIlj8ZCMGuhaDn4/sh4NVVReBVewKrluRFDTiRIhCAQB8EEIJ9+AErIHAoAohBWTxZRAiWPzIIweGF4KYi0OkFktsvfrbLRzZ3QAACEJhHACE4jxt3QQACCwkcXAwuniwiBMsHIEJwaCH4UkRubr3lMhDMZvGzXT6yuQMCEIDAPAIIwXncuAsCEKhA4MBicPFkESFYPgARgkMLwS4iuvYUXKf8CeAOCEAAAm8SQAgyIiAAgc0JBM7dbG5jZQMQgpWBRqpDCCIEI+NkqgxCcClB7ocABHoigBDsyRvYAoEDE0irg3tJ5u3RBT+bcCdCcIOxjhBECC4ddgjBpQS5HwIQ6IkAQrAnb2ALBCCwCwJrBJRga2j5UEEIIgTLR82bdyAElxLkfghAoCcCCMGevIEtEIDALgggBPt0I0IQIbh0ZCIElxLkfghAoCcCCMGevIEtEIDALgggBPt0I0IQIbh0ZCIElxLkfghAoCcCCMGevIEtEIDALgggBPt0I0IQIbh0ZCIElxLkfghAoCcCCMGevIEtEIDALgggBPt0I0IQIbh0ZCIElxLkfghAoCcCCMGevIEtEIDALgggBPt0I0IQIbh0ZCIElxLkfghAoCcCCMGevIEtEIDALgggBPt0Y49CMNn0roh46pQXp3+q6v/d/OpF2PTom0vwe+HVfGDQAAQgcAgCCMFDuJlOQgACaxJACK5JO95WL2LDzDzP5B0R+WTC+pci8lhEHrQUhb0Im158kxtNvfDK2cnfIQABCEQIIAQjlCgDAQhAoIAAQrAA1opFtxYbZubi74GIvF/Y7Ucick9VXxXely3ei7DZ2jdZUKlAL7yi9lIOAhCAwBQBhCDjAwIQgEBlAgjBykArVbeV2DAz3/r5MLMCmOvlr76KqKpPcwVL/t6LsNnKNyWsvGwvvErtpjwEIACBSwQQgowLCEAAApUJIAQrA61U3RZiI4lAF28fVurG56rqorLK1Yuw2cI3cwD2wmuO7dwDAQhA4CoBhCBjAgIQgEBlAgjBykArVbeF2AgIhzm9+1RV/fzg4itg38e1VyEvGb2Fb+bA64XXHNu5BwIQgABCkDEAAQhAoDEBhGBjwDOrX1tsBMbBzJ6IbxO9WSOITC/CZm3fzAXfC6+59nMfBCAAgXMCrAgyHiAAAQhUJhAQAF+r6v0lzZqZTd2vqsXv91Em43O5rdk/M7shIj/PtTVw3xNV9eAzi65ehM2avlkCzMw8YM87E3V8pKo/LWmDeyEAAQisRaB4orCWYbQDAQhAYFQCCME+Pbem2DAzP8f3WZDEcxHxc4SeS9D/TQmN8yoXiw6EYNBDqViLDzBlFlAaAhCAQD0CCMF6LKkJAhCAwGsCCME+B8JaQjAFiPklQOFJSgvxRvJ4M7snIr5inBOEj1TVcxLOvhCCZegQgmW8KA0BCPRNACHYt3+wDgIQGJAAQrBPp60oBH3L5vcZCpMizsx8ZdBXCafE4K+q6qkpZl8IwTJ0CMEyXpSGAAT6JoAQ7Ns/WAcBCAxIACHYp9NWFIK+mvfVBIWXKdjLZIJ4M/PVvu8yNBdtD0UIlo1VhGAZL0pDAAJ9E0AI9u0frIMABAYkgBDs02krCkFfybs1QSEcLMjMfNvo+xN1LUrvgBAsG6sIwTJelIYABPomgBDs2z9YBwEIDEgAIdin0zoSgmHx1lqota4/OhLW8k3UnuvKIQSXEuR+CECgJwIIwZ68gS0QgMAuCOxYCIa2NPbqxLXERk1xVbOuS35pXX90LKzlm6g9CMGlpLgfAhAYgQBCcAQvYSMEIDAUgR0LQfeDpzq4raqT59t6dNhaYiMgrr5VVY8Mmr3YGvo7omeqejsLrHEBVgQbA6Z6CEBgVQIIwVVx0xgEIHAEAjsXgsOKwRWFYC5YzK8iciMnpoPBYj5Q1TfST5Q8YwHRGt7GWtLu1bJr+WaJjX4vQnApQe6HAAR6IoAQ7Mkb2AIBCOyCwMBC0FMRRPLfncTgnSUiZG1nryU2zCySPuKJqnq5ixfpI97CsvmKoJk9FJHPpsatqjKvWvvBpj0IQGA2AV5Ys9FxIwQgAIFrJ/G5FaFw1MgJoWAtJqSRye5Zu76y5dtEfxphLKwlBJ2FmfnW2VxC+L8Qkb/ryePPBTUJ5S+Opk2FYPS5QAiO8CbARghA4EQAIchYgAAEIFCZwKgrgknA+Krg40z6g3Niw4jBlYVgdvXoyrD7P0Tk//L8ggEBebp1UQ7B5O9cqovDbw2NikAR+VVV/fnhggAEIDAEAYTgEG7CSAhAYCQCIwvBE+eCya/fMoQYXFkI3hARXynNrQrOHdqTW0ujlXJGcJpU4XPwSFXvRtlTDgIQgMDWBBCCW3uA9iEAgd0R2IMQTKtFpatan6uq39PltaYQTPw8Mug3DWC48L5Z43wmQvB67xSKwGGj6TYYn1QJAQgMQgAhOIijMBMCEBiHwF6E4N7E4NpCcCa/yED/VFV9++7iCyF4GSEicPHQogIIQGAAAgjBAZyEiRCAwFgE9iQEk5jx7W7fFXihy5XBjYRg6ZnLHOaqbBGCb+NGBOaGIH+HAAT2QgAhuBdP0g8IQKAbAnsTgjPFYDhp+lqO20IInvpmZg9E5IsFffXtoHdrrQSe2UWwmDOnIAIXjFBuhQAEhiOAEBzOZRgMAQj0TmCPQnCmGOwqeMaWQjDxm3tm8JGI3MsloJ/zXLAi+AdqiMA5I4h7IACBkQkgBEf2HrZDAAJdEggIwcURH82sSR7BHNAkpvx8WjQaZjdisAMheFtEfsgxTn9/mdJ4PKgRFOa6NhGCr3M++vZdX7GdTBZ/xpDAMMFBTDEIQKBvAgjBvv2DdRCAwIAEAkLQe7VIIG0lBNPKlue68y2FUTH4TETutFjRKhkeAwhBFxi+avjTWqyOLgSTCPSx/GFwLCECg6AoBgEI9E8AIdi/j7AQAhAYjEBQCM4Wg2aWDd6iqk3f72ZWKgY3n0APIASfqaqvGq52HVkIIgJXG2Y0BAEIdEqg6USh0z5jFgQgAIGmBCJC7cyAopXBaN2theDZyqDnDRxiNQUh+PawP6oQRAQ2fQVSOQQgMAgBhOAgjsJMCEBgLAJm9lOBQAqJwagIdFJrCMEkBv18VenWOo9+6XxWvRCCQwvB07bZWmPGzwRGP2B0sbW5VsepBwIQgMCJAEKQsQABCECgAYEZKw6TYrBEBIrIqlsMZ/TVUyHcXlsMIgSHFoINntJQlaGPNKGaKAQBCECgMwIIwc4cgjkQgMB+CMwQSBcnnYUicCuRVZo4fXU7EYIIwcK3CyKwEBjFIQCBsQggBMfyF9ZCAAKDEVgqBkcQgecuKczF5mLQo4n61tLmF0KwayH474rI/9h8EMQbQATGWVESAhAYlABCcFDHYTYEIDAOgblicDQRePJIoRj02z5XVQ860/RCCHYtBD1txjdNB0C8ckRgnBUlIQCBgQkgBAd2HqZDAALjEJghBv9PEfk3gj1cfZtlzq4exSBCECGYG7dL83sG6qcIBCAAgW4IIAS7cQWGQAACeycwQwxGkHQnAk9Gz1jRvNEykTpCsGsh6PkTf4gM+IZlWAlsCJeqIQCB/gggBPvzCRZBAAI7JlBZDHYrAmeKwY9bnhdECCIEJ14tiMAdv3fpGgQgcJkAQpCRAQEIQGBlAkkMPhaRWwua7l4EzhCDCEFVXxlb7RooofwrEfFcgi2uh2ucUW1hOHVCAAIQWEIAIbiEHvdCAAIQWEBgxjm6U2vDiMAzMXgzJZ5/ZwIZQhAheN3wWDU35oLHmlshAAEIDEMAITiMqzAUAhDYI4EZYnA4EXgmBj1NxNQqKEIQIYgQ3OOLjj5BAAJdEkAIdukWjIIABI5CoFAI+ta4u6r604h8tt6GyBnBt0fN1j45+0iQCxbDiuCIDz02QwACXRNACHbtHoyDAAT2TGCGCLzdMqpma9Zbiw6EIEKw9RinfghAAAIjEUAIjuQtbIUABHZD4Ggi0B2HELTuVr229gkrgrt5pdERCEBgQAIIwQGdhskQgMDYBI4oAhGCr4UwQvCaR7dHNmO/ZbAeAhCAQJ4AQjDPiBIQgAAEqhE4qghECCIEpx4ihGC1VwwVQQACEAgTQAiGUVEQAhCAwDICRxaBCEGEIEJw2fuDuyEAAQjUJoAQrE2U+iAAAQhcIHB0EYgQRAgiBHk1QgACEOiLAEKwL39gDQQgsEMCiMDfnLp1YJKttx9u3f6lR2trn5xs6pHNDl9FdAkCEIDAGwQQggwICEAAAg0JbC0C0wT7XRG5mbrpOQhfqaond1/12lp0bC02tm4fIbjqcKcxCEAAAt0TQAh27yIMhAAERiWwlQg0szueeF5EPsmweyIiD1X18RqMEYJEDb1unPUoktd4JmgDAhCAwJYEEIJb0qdtCEBgtwTM7IGIfBHs4HMRWZwsPk2mvd0Pg+2einn791qvEiIEEYIIwcInk+IQgAAEGhJACDaES9UQgMAxCZjZDRH5Odj7WiLwvoh8FWzzumJfq6rX0+RCCCIEEYJNHi0qhQAEIDCLAEJwFjZuggAEIHA9gcA2t9PNtUTgQxH5rJJPHqmqbyutfiEEEYIIweqPFRVCAAIQmE0AITgbHTdCAAIQuEwgKARricCSLahRl32rqveihaPlEIIIQYRg9GmhHAQgAIH2BBCC7RnTAgQgcDACASH40qN4quqrJWgC7Syp/tPaQWQQgghBhOCSR5J7IQABCNQlgBCsy5PaIAABCHi+vNsi8sMEimeq6mUWXWb2QkTeX1TJ9TdXEavn1SME1xkXJeNha5+cbF3rmSlhQ1kIQAACeyeAENy7h+kfBCCwOoE1JrVm5uf4vgt2zrehnvIGugCNRhX9XFX9/GGVa2vRsYZfpkBt3f4l27b2CUKwyqNFJRCAAARmEUAIzsLGTRCAAASuJ7DGhN/MPDF8TtBdTAtRkGbipap6BNQq19aiYw2/IATnDZWtfTPPau6CAAQgMDYBhODY/sN6CECgQwKtJ7XB9BSTwWjM7N20SpgTkx+oqm9BXXwhBNkaet0gav3MLB68VAABCEBghwQQgjt0Kl2CAAS2JdB6Umtmd0Tk+0wvswIuKCirbQ9FCCIEEYLbvptoHQIQgMA5AYQg4wECEIBAZQIrCMFc8vgnqupiMXuZWS4HYbUk8whBhCBCMPtIUgACiwmk3yA/D34pKJmfF/+pdlToxUZTwSYEEIKbYKdRCEBgzwQ6EIJh8WZmOVEZrivnU4QgQhAhmHtK+DsE5hNIQcT8nR6JJu2Roe/XDAg233Lu3IoAQnAr8rQLAQjslgBC8LJrEYIIQYTgbl97dGxDAmZ2U0R8d0fuzPclK/08+V1V9QBkXAcjgBA8mMPpLgQg0J5AB0Lwuar6xCB7mdljEflkoiArglmKsQKtx0XMijdLbS3OT9b0yGYOT+6BwNoE0irgAxF5Z0Hbv4rIPVYHFxAc9FaE4KCOw2wIQKBfAq0ntYH6Hc7HqnrKHXgRVvqK/GOGJMFiKg21gN+eqeqlMz2VLHi7GoRgM7RUDIHmBIKBw0rs+JSzgyW4xi+LEBzfh/QAAhDojEDrCX9K/fBLptt+/uOmqr66VI70ERfpNRVircfFnMcAITiHGvdAYHsCKeqzb+dcshJ4tSO+Mui/G1VSBm1PCQtyBBCCOUL8HQIQgEAhgTUm/IEJvFt9cbtP+orsW4lyAQXCW0wjiAI2Z1cxI+1cV2YNv0zZt3X713wQ8FXjWxN2N/XJqd0e2SwZa9wLgdYEAu/TuSY0/SA21yjua0MAIdiGK7VCAAIHJrDGpDadC/kuiPn/FRETkX9ORP6piPwzwfuqbQv19gITl6aiYw2/IASDI+tKsa19M89q7oLANgQCz8tSw5q+i5cax/31CCAE67GkJghAAAKvCQR+pKt8cTUz3xY0J0pcxFMvVfVGpGC0DEKQqKHXjZW1npnoWKUcBCbGqr8X574bPYjXuxXo/rGI/NVAPY9SNNFT0bsi8lngvnAu2kBdFOmYAEKwY+dgGgQgMCaBtSa1wWAvcyFW/yKMEEQIIgTnPo7c1wMBM/Mt9V/0YEvGBj8WcOdSwLD0++TRonNnC9+77oz5AP3HxCABhGAQFMUgAAEIRAmsJQTdnsItotEuVEsZcd4gQhAhiBCMPoKU642AmXmevshqWg+mT0b/DEYbJYJoD55sbANCsDFgqocABI5HYE0h2EAMPlJV3z5U/UIIIgQRgtUfKypcgcBgIjAU5CtwtKDJB8EV3EUTBQQQggWwKAoBCEAgQmBtIXgmBpcmFf5SVb2OJhdCECGIEGzyaFFpQwKDiUAnERJwZnZfRL6aQBeqpyF6ql6BAEJwBcg0AQEIHIvAFkIwiUEPYOA/7qXbl56JyD1V9eAzzS6EIEIQIdjs8aLiBgQGFIE1heC3qnqvAVaq7IgAQrAjZ2AKBCCwDwJbCcETvZRo2H/A/8OUMuI6sH9PRP6z1gLwzK5Nc9Z14JfbIvLDxCivEk225CnaWpyfjY3u2JRwpOz+CAwqAmsKQVYE9zes3+oRQvAATqaLEIDAugS2Fhy9CK+r1LcWHVv7Zev2Lz0FW/sEIbjuu4nWYgQC2yZjFW1TijOC23AfslWE4JBuw2gIQKBnAr1M+HuZ5PciTLf2y9btIwR7fmtgWy8ECiMxvxSRFwW2e9mS8peq/msi8u9n2vxcVT3K6cUr2EeihhY4dtSiCMFRPYfdEIBAtwR6mfAjBN8cIlv7Zev2EYLdvjIwrBMCQYF0sva5iNxeO9eemXlC+l8yyDyP4F1V9XyBb1wpdYSLRPIIdjLutjQDIbglfdqGAAR2SaCXCT9CECGYe8B6GSO9PDM5Xvx9vwRGEIEn+mbmAu+TgDc8EJiLPl+F9GBinhroVuC+J6p6J1COIoMTQAgO7kDMhwAE+iPQy6S2l0n+2eSFYDEEi7n4wPbyzPT3NsGiNQiMJAKdR+B5WYrtY1X19zXXzgkgBHfuYLoHAQisTyDwI71KdEiE4Ju+39ovW7d/6UnoZYz0yGb9NwctbkFgNBF49mEtuipYipXVwFJiA5dHCA7sPEyHAAT6JNDLpLaXST4rgr8R6GVcnD81vYyRHtn0+XbBqpoERhWB6X3iZwV9y2furF8JMj9beGPtc48lBlK2LgGEYF2e1AYBCECgmwl/L5N8hCBCMPdaQAjmCPH32gQCY+68SRdIN1V1acTPqt0ws5si4ls4a4hB76MHv/mpqpFU1jUBhGDX7sE4CEBgRAKBCQZbQy87tum5lK39snX7l5D38rGgRzYjvnuwOUagUEB1LZAK+3IdoK77GPMqpeYQQAjOocY9EIAABCYI9DKp7WWSz4ogK4K5F0Yvz0zOTv4+PoFC4TSEQDIzjwjq0UEjEUGvOtEji3qqia5WO8cfaWP0ACE4hp+wEgIQGIhAL5NahOCbg2Zrv2zdPiuCA71EMLUJgT2KwHNQ6czjPRH5MADQ8yA+mEo8H6iDIoMTQAgO7kDMhwAE+iPQy4QfIYgQzD0dvYyRXp6ZHC/+Pi6BvYvAK4LQzw7eTv88qMzpepXOFD7lLOC4Y7mm5QjBmjSpCwIQgEBH0SF7meSfBsXW9mwtNrZunxVBXk9HJmBmHgQlslI2xHbQI/uSvtcjgBCsx5KaIAABCLwm0MuEf2vhdXU4bG3P1n7Zun2EIC+ooxJIq4E/BvqPCAxAosh+CCAE9+NLegIBCHRCoJcJ/9bCCyH4JoFexsW5Vb2MkR7ZdPI6wYwKBALj69TKp6rqidq5IHAIAgjBQ7iZTkIAAmsSCEw6SB9x2SGkj1D1cz2rXQjB1VDT0IYEAu9kt+5zAqds6CSa3oQAQnAT7DQKAQjsmUBg0oEQRAheIrDKuDhvGCG45zcRfTsR6OWdjEcg0BsBhGBvHsEeCEBgeAK9TDp6meSfTcaeZvJcsSLIiuB1z//qInn4FxEd+J1AL+9kXAKB3gggBHvzCPZAAALDE+hl0oEQfHMobe2Xrdu/9GD1MkZ6ZDP8i4gOIAQZAxDIEEAIMkSGJpAigd0REc+Zc54rx8NE+z/PlfNi6E5i/HAEepnU9jLJZ0XwNwK9jIvzB6qXMdIjm+FePBh8LQHGF4MDApcJIAQZGUMSMLO7InJfRN4PdOCJiDxQVd+WxgWB5gR6mXT0MslHCCIEcw9dL89Mzk7+PiYBxteYfsPq9gQQgu0Z00JFAmkF0EM7RwTg1ZYficg9VX1V0SSqgsBbAurJxAAAIABJREFUBHqZdCAE33TN1n7Zuv1Lj2ovY6RHNrza9kOA8bUfX9KTugQQgnV5UltDAmkV8LuFTTwXkTtsF11IkdsnCfQy6ehlks+KICuCuVdGL89Mzk7+PiYBxteYfsPq9gQQgu0Z00IFApVE4MmSl36mkJXBCo6hiosEepl0IARZEcw9or2MkV6emRwv/j4mAcbXmH7D6vYEEILtGdPCQgJpO+iPC6u5evtzVfUAM1wQqE6gl0lHL5P8E+Ct7dnaL1u3f2mgb+2Ts7FxW0R+mHgYSR9R/U11nAp7fPaOQ5+e9kwAIdizd7DtNQEz86ifc84E5gh+raoecIYLAlUJ9DLp6GWSjxD8jUAv4+J8sPcyRnpkU/WlQGWbEmB8bYqfxjsmgBDs2DmY9nri5NFBl54LvA7lryJygy2ijLTaBHqZdPQyyUcIIgRzz1gvz0zOTv4+JgHG15h+w+r2BBCC7RnTwgICBauBHhHUo4l6RFDf8ukC8sNA039PRP5uyjkYKH5tkRcEoFmCb1/39jLpQAi+Oa629svW7V96ynoZIz2y2ddb6di9YXwd2//0/noCCEFGR7cEgmcDfVXvtqp68vg3LjO7JyLfdNvB3wzzwDWe35C0Fp07qsS8XiYdvUzyT+y2tmdrv2zdPkKw5Cmm7J4I9Pjs7YkvfRmXAEJwXN/t3nIz8/N7X2U6+tElEXg28XwgIl8MAMvTWrigJcfhAM7KmdjLpGNr4XXh44x/9Lg1we9jVfUyTa6t/bJ1+wjBJsOKSgcg0OOzNwA2TDwAAYTgAZw8ahfNzLd6fjJh/yNV9S2g115m9q6IeLCZdwbggBgcwEkRE3uZdCAE3/TW1n7Zun2EYOTppcweCfT47O2RM30ajwBCcDyfHcbiwCT2U1V1sTh5BerJVbHm3xGDa9Ju1FYvk47A2G+6AncV79b2bO2XrdtHCDZ64Km2ewI9PnvdQ8PAQxBACB7CzWN2stakMbjFtCdIiMGevDHDll4mHbWeoRkILt6ytT1b+2Xr9hGCtUYy9YxGoMdnbzSG2LtPAgjBffp1F72qNWkMbDHtkRdisEevBG3qZdJR6xkKdjtbbGt7tvbL1u0jBLNDlAI7JdDjs7dT1HRrMAIIwcEcdiRzA5PGb1XVI4Neew12RvBqPxCDgw74XiYdgWeIraFvjrFnqnq71bDrZVyc96+XMdIjm1bjgHrXJ8D4Wp85LY5BACE4hp8OaWVgS6enjrg5lb8vUIez/R9E5F9ZCNlzF7YISIMYXOiYLW7vZdLRyyT/5IOt7dnaL1u3z4rgFm8D2uyBQI/PXg9csAECCEHGQLcEAi9ut92F0p1LYtDMPKLod5kOvlTVG2tDMLOHIvJZsF3EYBBUL8UCY7fpylMvwuuqPxCC5quNP0yM01XGBSuCvbwpsGMtAr28k9fqL+1AIEoAIRglRblNCJiZp354P9O4rwx6vkDPP+blfXXOReBU6olTldntpa06XigGs6kyWtlJveUEepl0bC28EIJvEuhlXCAEy59p7hibQI/P3thEsX4vBBCCe/HkTvsR3Nq5pPcfTG0tXVJx5F7EYITSeGV6mXQgBPsSYr2MC4TgeO8ULF5GoMdnb1mPuBsCdQggBOtwpJZGBFKwl58Cq4JzLNhsNfDKRKxkmygrg3M8vfI9vUw6EIIIwdzQ72WM9PLM5Hjx9zEJML7G9BtWtyeAEGzPmBYWEjCzOyLy/cJqrt7e1bk7VgYre3fj6nqZdPQyyT+5Y2t7tvbL1u1feiy29snZ2Oju/OTGrxGar0igx2evYveoCgKzCSAEZ6PjxjUJVN4i6mcKb6uqrzR2cyEGu3HFYkN6mXT0MslHCP5GoJdxcT7AexkjPbJZ/CKggm4IML66cQWGdEYAIdiZQzDnegKVxGCXIvBsosw20R08BL1MOnqZ5CMEEYK5x7qXZyZnJ38fkwDja0y/YXV7AgjB9oxpoSKBtE3UxdKcnH3PPJrolsFhIihYGYxQ6rtML5MOhOCb42Rrv2zd/qWnppcx0iObvt8yWFdCgPFVQouyRyKAEDySt3fS1xRA5p6I+L+IIHwpIvdV1QXkEJeZPQ6mv/D+EECmM6/2MunoZZLPiiArgrlHtJdnJmcnfx+TAONrTL9hdXsCCMH2jGmhEYEkCD2QjAcZ8KTw74rIhyLiK39+eV7Bx72dBYzgSH1z+70/kQsxGKG0UpleJh0IwTcdvrVftm7/0vDvZYz0yGal1wXNrECA8bUCZJoYkgBCcEi3YfQRCMwQg5+qqq8kcm1MoJdJRy+T/JM7trZna79s3T5CcOMXA81vRqDHZ28zGDQMgTMCCEGGAwQ6JlAoBp+pqq+Ocm1MoJdJx9bC66obtrZna79s3T5CcOMXA81vRqDHZ28zGDQMAYTgmGPAzG6mbZC+BfL88i2EP6nqqzF7htVTBArEIEKwk6HUy6Rja+GFEHyTQC/j4tyqXsZIj2w6eZ1gRgUCjK8KEKlilwRYEezcrWbmZ988KMrdQGCUJyLyQFVdGHLtiEBQDCIEO/F5L5OOXib5J7dsbc/Wftm6/UuPx9Y+ORsbJJTv5P21RzN6fPb2yJk+jUcAIdixz1LevGhkzPOeDJEmoWP0XZrGD1mXbrloVC++6mWSjxD8jUAv44IVwXHeJVhah0CPz16dnlELBJYRQAgu49fk7uDqT65tT5x+h9XBHKZx/s4PGb4qJYAQfJPY1s/Q1u1fGj+9jJEe2ZQ+b5TvlwDjq1/fYNm2BBCC2/J/q/VKIvC83s9Hyp/XmTu6Mocfsq7cMWlML77qZZJ/grW1PVv7Zev2EYLjvEOwtC6BHp+9uj2kNgjMI4AQnMet2V1m9lNB7riIHb4yeHvEXHqRzh2pDD9k43i7F19tLbyuemxre7b2y9btIwTHeYdgaV0CPT57dXtIbRCYRwAhOI9bk7vSmcCvGlT+XFU94ijXwAT4IRvHeb34amvhhRB8k0Av4+Lcql7GSI9sxnnjYGmOAOMrR4i/H5UAQrATz6fooD83NOdrVb3fsH6qbkyAH7LGgCtW34uvepnkn9Bubc/Wftm6/UtDfGufnI0NooZWfAdRVf8fYfARBHoggBDswQu/RZN7ICJfBMzxrZ6PReRFKnsnuJX0V1W9mn8w0BxFeiHQ4ySyFza92dGLr3qZ5CMEfyPQy7g4f156GSM9suntvYI98wkwvuaz4859E0AIduJfM/Nk8O9kzHnkOQWvJo43MxeDDwP3/6mI/P1OuryGGa/2dDaSH7I1hkydNnrxVS+TfIQgQjD3ZPXyzOTs5O9jEmB8jek3rG5PACHYnnG2hcALyut4pKqeVP7iZWZ+BvDHbGPHK/AypdHwIDxDX4FxQkL5Tjzci68Qgm8OiK39snX7lx6PXsZIj2w6eZ1gRgUCjK8KEKlilwQQgh24NRAkxreD3ri6EnjVdDPzVcHPOuhSbybsInIqP2S9Davr7enFV71M8k+ktrZna79s3T5CcJx3CJbWJdDjs1e3h9QGgXkEEILzuFW9K3A+8FtVvZdrlFXBSULDi0F+yHJPQD9/78VXWwuvCx+rnorIrQlPfayqXqbJtbVftm4fIdhkWFHpAAR6fPYGwIaJByCAEOzAyYHJ2qeq6gFispeZWbbQcQsMLQb5IRtn4Pbiq8C7panwQgi+SaCXcXFuVS9jpEc247xxsDRHgPGVI8Tfj0oAIdiB5wM/xAjBen4aVgzyQ1ZvELSuqRdfBd4tCME3B0PTc7a9jAuEYOs3APX3RqDHZ683RthzTAIIwQ78ztbQ1Z3gAWRu5s5crm5VpkF+yHrzyPX29OKrDoWgb3H/ZsKTH7WM9BvI1xrahj93JJqZp/D5ZeL+yaBgc9udui9wRr2pT062be2bFmypsx8CvbyT+yGCJRD4jQBCsIOREPghJlhMfT89F5HbI4lBfsjqD4JWNQZ81VRwnE2uc8LrvTWfgSSEPAfqpVQ5TVfjzpj4NvtPLvje37P+geiUo7XJ8Jj48LdK+1c7lQSYR1XezCdnvrkun+4/FZF/KCL/pIlTjlmpj3P3+8Pa74AUr+C+iPSUu9ht+XDC1au8f4451Oh1zwQQgh14JzBpdCtrpI/4CxH5Rx10ubUJU8EoztseSgwGxgk/ZK1HVkH9ZuYTrfevuWWVLZlJeHnwlUsToK9V1Sdrq15pkuhi7JzNs5TmxfOpNr0SE4+wfC4GV0szk9p37l+cddTb9xyxobPgtQFd45MnInK3tkiYsv0aNrW7S31vEqj6O2hmnubquwEh8/s5oNMweTkBhOByhlVqWCmhfPisYZVObVRJ4Q9R1R/Bll1GCLakW7/uNLl2EXZ1pWVVAZYm177Sckot46Ljgar6/7fZlcazt/+i9SrcpU6mlbAb/reWkUqvA7x1+9cwuZ3+/0184m0H3nObjdkdN1zld3Bw3yEEdzzA6dr1BBCCnYyOwDnBk6W+fci/Gp+2L93JbHf4/T5V7WmbRlPyexSDgR9ZfsiajqryypMI8y/kp2fv6Raio9xy7oDANgQKfgu3MXC/rS4SgxMfvkYhxu/nKJ7CzqoEEIJVcc6vLHBQfn7lv9256irEUmNr3L83MYgQrDEqqAMCEOiZQCDAUc/mj27bLDG4AxHofkMIjj56sX8WAYTgLGxtbgoEjZnb8HNVvTn35pHvKxSDvmWuaaCIhSxzh939fJX/kHP1Q8CDMfg2zJ7HVT+0sOTwBBr+Dh6ebRBAkRjciQh0NF9uvV0+6B+KQaAqAYRgVZzLKzMznzhORbYqbWTYvHmlHb2uvJl5YIjT+aha1VIPBKIEDv8MRkFRDgKZKKYAWodASAzuSASGIrOvg55WILAuAYTguryzrWWi/GXvv1Dgc1V1IXToCzF4aPf30PmXqvo6MAkXBCAwTWBHAmNkV0+KwR35yCMWe8Re/wjPBYHDEUAIdujySmLQv3DdITDFHxyMGOxwsB/LpFVSRhwLKb3dK4H0O3jIIw2NfepMvwm2cVEMFopAn4t4wKzmqWGCffq9GPOjUmKU3yMBhGDHXk1nJTwh9KVEv1OW+xcuz//EuaQrlBCDHQ/4/ZuGENy/j+khBLonUPg7+IYYnCECb7Pa1v2QwMADE0AIdu78dF7CxaB/UcsJQk8A7IEpPHcZ1zUECn8E4QiBWgQ+4ONMLZTUAwEILCFQ+Dv4WgyKiG9vv5Qb9TpTOJqyxEnc25RAisTuK+RXU6v5NmHPpXqI7cIIwabDrG7l6Uucv4yvDlp/Mf+kqt1tvahLoF5tZua5GD+pVyM1QWCSwBNV9ZyfXBCAAAS6IFAoBv93EfnXROQvBY1HBAZBUWw9AimSvP8WR+Z/Hkne59e+wLJbUYgQXG/80VJHBNL5k/si8kVHZmHKPgn4Sr1v1eZDzT79S68gMCyBQjEY7SciMEqKcqsQSALQ53zvz2xwt0GFEIIzRwS37YNA2no7YjTHf0lE/krywm6/VO1glPn2Es7q7sCRdAECeyVQWQwiAvc6UAbsV5rj+Q6wWmnZvlZVF5S7uRCCu3ElHYEABCAAAQhAAALlBCqJQURgOXruaEQgnQF0EZiLr1Fqga8OelT+XezyQQiWup/yEIAABCAAAQhAYGcEFopBRODOxsPI3UlbQb9r2IfJPJsN261eNUKwOlIqhAAEIAABCEAAAuMRmCkGEYHjuXq3FhemOFnC4ZmqegDHoS+E4NDuw3gIQAACEIAABCBQj0ChGPwHqvpH9VqnJgjMJ5ACAXrchLlBYUob/1ZVPcXbsBdCcFjXYTgEIAABCEAAAhCoT6BQDD5SVc91zAWBTQkUjlu31bd4eoqI03k/Dx7oq3wlQvLjkfN3IwQ3HbI0DgEIQAACEIAABPojUDipRgz258JDWZS2hP4Y7PQjEbl/XVRvM/Ncgw+CgnDoLaIIweCIoRgEIAABCEAAAhA4EgHE4JG8PXZfg2P11xTx01cBs1ewTq9n2FVBhGB2GFAAAhCAAAQgAAEIHJNAwWTYAbEyeMxhsmmvU77AnzNGuAi8rapFuZeD43/YcY8Q3HTo0jgEIAABCEAAAhDom0BwMnzqxLCT4r69gHXXETAzD9jyTYbQl6rq2z2LLzNz8TiZlF5Vh9RUQxpd7EFugAAEIAABCEAAAhCYTcDMfDvdrWAFiMEgKIotJ2Bmnjj+k4maXqqqB4KZdaXk9D9kbh5yeyhCcNaQ4CYIQAACEIAABCCwLYEULt8DW3ikw6sT3RcpIuLT64JilFif2nIxOLkyclanB+R4WNIGZSEwk8D/JCL/4sS9i3NdBlYFv1bV+zPt3+w2hOBm6GkYAhCAAAQgAAEIlBNIZ6J80vlZ8G4XZQ9Kz0ddrXuGGAyaRzEINCXwwdKPIWbmz9tXE1YOuQqOEGw67qgcAhCAAAQgAAEI1CMQmJBONbY4ATZisJ4vqWkdAjXO76WUEt9PWDxkGgmE4DpjkFYgAAEIQAACEIDAbAIVBZgn0fboiack2sU2VbSluG1ugEAhgSoCLXBOsEo7hX1bXBwhuBghFUAAAhCAAAQgAIF2BBoIL8RgO3dRc18EFgWKOXUFIdiXU7EGAhCAAAQgAAEIHIJAYfqGKJMnquqBZmZfDQTqbFu4EQLXEai0NTSXooIVQYYgBCAAAQhAAAIQgEA9AoGzSUsaqxFN8d0UnTQaTXSJvdwLgTkEPlVVTzEx+wqkqCBq6Gy63AgBCEAAAhCAAAQg8BYBM/M0EO83QvOrqrqQW3SxMrgIHze3J7Aoomca379kzFz8UaU9hrdb4IzgFtRpEwIQgAAEIAABCGQImNldEfkuAOqlp4cQkZ9S2Zsi4lvZIgKyygQ2TZbd3kXbTQN9pQgErhL4KyLyr2ewfDQ3fYqZ+bP1Rab+xSkqtnArQnAL6rQJAQhAAAIQgAAE8kLQt7N9kil27WpH8GzhkGebGDwQOBEwM//w8WOGyKwASYEgMd7sc1V1G4a7EILDuQyDIQABCEAAAvsmkCZf/hWec2fTrs6KuMDZJqkRTGPfI5Le9U4guIW6SAwmgflURN7J9L/KqvoWjBGCW1CnTQhAAAIQgAAELhJoHBxlb9Sz29HM7IaI/Jzp+Meq6hNeLggMSaBgG7WLwXu58W5m90XkqwCMX0XkxpK8nIE2mhVBCDZDS8UQgAAEIAABCJQSCH7ZL612j+X/sar+y5GOmZmfHZxaXUUIRkBSpmsCgXF+bv8zEXno52r97GA64+rbO/2Mq/+LnK/1+r5UVd+9MOSFEBzSbRgNAQhAAAIQ2CcBM7N99qx6r/6Jqv7zkVoD20OHnsxGGOypTFrlPQmWWxf65iLHz5c+VlWPOnuIK3ieryaLYc8GniAgBGsOB+qCAAQgAAEIQGARAYRgGF+JEPRtn5cEw6mxxXnWwlZTcDaBJAB9y+JnBZU8EpH7RxGEBVs6CxBeLOpbQm+OznVzIXi2FHuVsi/VvlrqpT3cP8FoD92jDxDYM4EXo/9I7Nk59K1PAoXbu/rsxDpW/X+q+s9GmjIzn09NBbxga2gE5IZl0hk434KYC1xyyUoXLX4uzrdC7v4KRstdymF2OoqlDde8f3UhmETNaTnb9+JO7cH1getfsU7L24cThit+2ag5rqgLAhD4A4EnInKXD1sMCQjECGywvStmWJ+lsls6zczzCX6TMT8bdKbP7h/DqorCZlFi9ZFoV2R2tduuTe7kgs2Mwmo1IThzOfsqx6Mtb0cTyY4y3rATAkcl8ERVSbJ8VO/T72ICSQy6gMnl0Cuue2c3+KT09nWJsoP51V6qqkcW5eqQQDCZeYnlX6uqby/d/RX8CFLC4WUSgR58aRdXcyGYVgB9wH1Rkdi3ab/zblcIk3D2gTZnC0BF1FQFAQhUIsAX90ogqQYCRyEQFAEuBv0M2BuRCwsmwd+qqoturs4INEylcpgzoeljiD8bU2dkI57fpfZoKgTTFz3fjxwNwRpxxKnM7lT5eecDEb5KWFEWAhDYngBncLb3ARZAYCgCwRyApz65IDytVPjRm+iHZD5SdTgq0kKKR/yM+rGkFz6H9kAnu11QuQojiWr/4FEiCP2Z8uNpuw2200wIFiR2LBm4V8vu8vBrwy9AS1hzLwQgsIwAQnAZP+6GwCEJNDzr5DxZDex0VK0QI+IwW0SvLLT4Nmg/quEfS/y/zz+aeLJ5F8f+QeWpqroI3PXVRAiuJALPHfP5XiIhNf4CtOvBTOcg0DEBzuB07BxMg0DPBBrOC/xj+o0jrQr17OcLK1i5SK9+y+sFkZQv8NVZQEY/kpXbjcfv0kgDopGt1YXgBiLwhGYXX9vZEtpopFMtBLYjMBnMYTuzaBkCEBiFQKNIql2Evz8TLwSs+cOAdBa5XIG+euWBgt7a3pmYetT9DzNj/DBnBUd51te2s6oQTAcyfeCV7mc+39fuDEr2tp+YDZ/YsWBL6LO1BwrtQQACswj49pIH5BKcxY6bIACBMwKVP7R3sZNqwbzx6GMjO+cNni895PbQow+e8/7XFoI+6cl9fTi17wdVPYrP40uTpDSAfQ+vL3nnlrdPdT5T1dsjOrhg6wdh6Ed0MDZDAAIQgAAEFhJIK4N+bqn0g/v5R/Nr000sNK/odkRgEa6rhUNnOwPnS4edNy+ix82/E6gmBAvCFF8Mczzlk8IDs0Mucwe3hLKfn4cXAhCAAAQgcGAC6cOxf0jPbR18Szz0knqLFFmLB/CfqurfzNUS2FL8XFV9Fx7XQQlUEYIFq1mzz8oUfDka7vBrwZbQLrZyHPRZodsQgAAEIACBbggkMeW7pnz31HU7p/wcma8gPuxli3rB+bVuWHdoyCNVvZuzK7I9VFWraIGcLfy9TwJVnB/ctz5bBJ7QFYjBYVYFC0Q0y/d9PkNYBQEIQAACENiUQJpLXF3Z+am3iKCIwGrD5L9Q1f88V1tgRXC4xZNcn/l7GYFaQjByNrBKVM/g6tkw5+gKtoR64k9PLMoFAQhAAAIQgAAEhiKACKzqruiKoKeR+GqiZRYZqrplvMoWC8H0YP+S6XpowEbxRcTTCEvdQVHrWL5UVT8PwAUBCEAAAhCAAASGIxCZuw3XqW0N/mBqgSC44ywUdGbbbtJ6SwI1hKDvTf8+Y+TkYC3tYNoi+mPmviorkKW2RcunB9RXUnMRUflaE4VKOQhAAAIQgAAEuiMQiF55bvO3IvJWbrzuOtXWIPIItuVL7YlADSG4ybKzmeW2o3adG8XMfIXvi8xIzOaJYSRDAAIQgAAEIACBXgkUikCC4iVHmpkfB8otFvg80QMGeSq2V2mRwRdofG4eufdGb+dIex3He7WrhhB8mAlh3ESQBYRUt8vdgcO7p/HGltC9Pnn0CwIQgAAEILBzAoG52jmBqseIRkdbmDptTnfhPYfazu6pIQSfisitCS5NIngGIpV2uaWSLaE7e4LoDgQgAAEIQAACbxEIzNMQgRPjpmC+OGf0kZd6DrUd3rOGEGxyVq9gVW1Et7EldESvYTMEIAABCEAAAoIIrDMIGs512YJbx0XD14IQ7NOFbAnt0y9YBQEIQAACEIDA9EqWJzr/LgjpuapezX8YvPUYxRpsEWVL6DGGTqiXawjBJqLGzP5jEfmvQ70cq1CXW1rHQoi1EIAABCAAAQisTSBFdfcjQ+8E2n4uIrcJVpInVXjWcqpCRGAe96FK1BCCuaihTQadmf33IvI3duitj1TVI6JyQQACEIAABCAAgSEIIALbuiltt/WI8xGRfcmYJgszbXtN7a0J1BCCHrb2mwlDX6qq50OpepnZn4vIX65a6faVNYmwun23sAACEIAABCAAgb0SKBSBL0XkJiuB5aPBzHw+7QswnxXc/cjvmUo+X1AXRXdGoIYQ9L3dqyZ3b3h4dkv3sk9+S/qN2k4v7asfQl7wQm4EnGohAAEIQGBVAim6pee8i6xUeTA83w7KzqcFXkpzC88X6P98Hn7O3hk738cpv6D7hgsCFwksFoJeq5m9yrwAqp57M7NcyorR3M2LcTSPTdh79sXu9kRCV/8i6i/pB4jCHTmfrkAAAhA4EIEkAn1O9mGg28x1ApAoAoE1CdQSgrmk8t6nKnuTzSy3FXVNfjXaepKW7Pk6VoPmhnWkH0Tfv1+yZcMt9m0b99gms6HzaBoCEIAABIoIIAKLcFEYAl0SqCUEI9tDHcCiQChpD3puG6q30yR3YZcexKguCKTtyr7CF9kac8lm/1J6R1X9yyoXBCAAAQhAoFsChSLQ+/GpqvpvJBcEINARgSpC0PsT3K7pk11f+fAVxKLLzHwftN+Xm2hX3YZaZCSFD0mgMHFujhFJXnOE+DsEIAABCGxKwMwiO8FONvK7tqm3aBwC1xOoKQT9PNQPQdjfpu2QfrZw8kpfnTxC0he5sunvrAYGQVFsOYFGgYsYw8tdQw0QgAAEINCAACKwAVSqhMBGBKoJQbe/MOGlrw76NoGHl7bDpQn23RQRKbcKeML3RFV95ZALAs0JFEZKK7HHn40bnBksQUZZCEAAAhBoTaBQBFaJDdG6T9QPgSMTqC0E300ha9+fAdWjKHqIWw+1P/f+rvLSJDHrK6X+z9mcR9U69deDxPi5sKdM/GeMmg1vKfzwUWrpI1X1DyFcEIAABA5JIB0J8R1BkYiUh2TUcaf5DevYOZgGgROBqkLQKy1MKlrLE92EJE6pAzyyqU/ioyuZJw4eQdTTCRAwpNbIaFRP8vPPwepPOX28+NV8P1NVfEBqiSBhikEAArsiYGYuAL/aVaeO0xlE4HF8TU8HJ1BdCG4gBrsQgTPOMk4NnWcpqA4pJTp9wIKrgReDI6UUKD7JyX0o+FZV/aMCFwQgAIHDEEi/p78cpsP76igicF/+pDc7J9BECK4oBnsRgb71c0nqgOuG2deq6oKBqzMCZubbmKe2ME+OzeDK+XNV9RVELghAAAKHIdAoCNdh+G3YUX6zNoRP0xCJhtnGAAAgAElEQVSYQ6CZEExi0M/7eYjhW3OMy9zzPOVd8wn5ZtcKCe59ddDzy2UjrG4G4WANB79WZw/JB8fOe/j+YAOM7kLg4AQQgkMOAJ+T3eb3akjfYfSBCTQVgieuBVvhIq7wlZb7qvogUrhlmcLoWUtM4QW7hF7lewOTlHDUTzNzgT+1RZRUEpX9R3UQgEDfBArPYPfdmWNYxxzlGH6mlzsksIoQdG5pFeUURGVuVFBfXfRgKpuvjgVXc2oOGV60NWkuqCuQQP6Zqvp24exlZh4YaGrF3P2++XjPdoQC1xHwc77+ztp05wLugcBoBILnsEfr1h7t9QjoXUVs3yNk+gSBVgRWE4LnHUjnozyqpp9/mpoE+7ZIn0g97imSZgpp/X0rp0zUyyHsDaBfbTIQzS4c5MXM/pGI/NUOuoUJ7Qh0cZa5XfeoGQJtCKQPrv4Bec7H4zZGUes5gUcpsB0fKxkXEBiUwCZC8BKrtN3u9Z96En0XRIDnA/Sv+7mIj6dbfRLogWT830++MpBWR10E+7/SH7lPVdXr4tqIQOBDQGhFkO1PGzlwm2ZfqqqfmeaCAAQgAAEIQAACXRDoRgh2QSNgROG5wK8jW1mTCPZtr5GvnuHzZ4HuUGQGgcAZQa81mwPQzP6WiPzJDBO4ZUwCnPcc029YDQEIQAACENglAYRggVsLVnB8z7xH+gznAUyrhB4A57OASaSVCEBqWcTMLFP/E1W9M1UmkIKiZReoe30CCMH1mdMiBCAAAQhAAALXEEAIFgyN4GrgoqAuwTZYFSzwW4uiZuYi/8NM3RfPdBaK/hbmU+c2BLKrxNuYRasQgAAEIAABCByRAEIw6PVg7rgqQSECkSTd6s9V1beTcm1AIBA59GSVrw77Su9pdXjOudANekiTlQlkV4grt0d1EIAABCAAAQhAYJIAQjA4QIIT/2wS8UhzaQuqC4epgDRMLCMwG5VJHwZKggY1soRqByBAZL0BnISJEIAABCAAgaMRQAgGPR7Yslk1KmAgRYFHV8V/Qf+1KBb8ODC36S/PVhHn1sF92xN4QQ7B7Z2ABRCAAAQgAAEIvE0AIREcFYHAHlVWA0/mBAPT7DnZuKfIeKiqXecnMjO385PgMIoWC+chjFZIOQhAAAIQgAAEIAABCJwTQAgGx0MgSuRHJVFCI80GA5JEqhq1zKLAO2t0Om0RfRoIHBM157mq+jlCLghAAAIQgAAEIAABCDQjgBAMoDUzn5j/OFW0xTZNM/vbHhQmYOKeixxJDD4Rkbu9r4LuebDRNwhAAAIQgAAEIHAUAgjBgKcDCcSfqertQFVFRRptOyyyoZPC3YtB5xQ51znBk9yQnQw2zIAABCAAAQhAAAJHIIAQDHh5QyHoWw5vBUw8QpFRxOANEbkvIp8FneIRJe8TUCRIi2IQgAAEIAABCEAAAlUIIAQDGANCsGrE0JNJgUilAet3VWQIMZhWB98VkTsi4tuKr57589QgLvKfsg10V+OTzkAAAhCAAAQgAIFhCCAEA64KJpN/r/aknq2hF50zjBgMDC2KQAACEIAABCAAAQhAYBMCCMEg9kDU0E9V1VMJVLvMzFMnTCWVr9bWYBUhBgdzGOZCAAIQgAAEIAABCPRFACEY9IeZ5c7rPVLVu8HqssXMzLcVfp8p+B+IyJ9nKxungPOLnq1DDI7jVyyFAAQgAAEIQAACEOiMAEIw6JBgRMgPagX9CAjPJucSgziaFSs8F4kYbOYJKoYABCAAAQhAAAIQ2DMBhGDQu5FcgiJSJY2Emd0TkW8ypn2rql5udxdicHcupUMQgAAEIAABCEAAAp0RQAgWOMTMXojI+5lbFuWDS4LTt6HmzgZ+pKoefXKXF2Jwl26lUxCAAAQgAAEIQAACnRBACBY4wsz8DNt3gVu+VNUHgXJvFCkQgVVWHkvtW7s8YnBt4rQHAQhAAAIQgAAEIHAUAgjBQk8HVwW91icicjeaUiJtB/VE5LmVQK/7Y1X1VcPdX4jB3buYDkIAAhCAAAQgAAEIbEAAIVgIPRjN81TrryLiK4MPrwsik1YZfaXxVtCUJ6rqEUUPcyEGD+NqOgoBCEAAAhCAAAQgsBIBhOAM0DMTvXuESz/T5+cM3xWRm+lfZAXwXFjeiK4yzuhat7cgBrt1DYZBAAIQgAAEIAABCAxIACE4w2lm5kLOt2Z+OOP2JbccZkvoJUiIwSVDh3shAAEIQAACEIAABCDwBwIIwZmjwcxupBW+khW9ma29vu1zVX24pII93FsoBg+3jXYPPqYPEIAABJxA+ujqRyf84ysXBNYk4Lu3Hh9xB9aakGlrewIIwQU+SFE+HwdSSixoBRF4FV6hGHykqj6R4IIABCAAgUEIFETRHqRHmDkggZcicmfPqboG9AkmVyaAEFwINH2xdDEYDfZS0qIHm/GX0CEihJaAQQyW0KIsBCAAgbEIFEToHqtjWDsaAZ+H3UYMjuY27I0SQAhGSWXKmZmnfrgXTP8QabUo/USkwr2VQQzuzaP0BwIQgMDrLaG3ReQHWECgEwIuBg8ZqK8T/pjRkABCsCLcdG7QBeFnC6p9JiL3WQWMEUQMxjhRCgIQgMAoBBCCo3jqUHZ65HdfGXx1qF7T2d0TQAg2cPHZAXfP9xfZMuovGN/+6fkGPcUEVwEBxGABLIpCAAIQ6JxAOh/4Y+dmYt7xCCAGj+fz3fcYIbiCi9PXzVPuwFOLHpHK//3EF6blTigUg/4y56vecuzUkCfgH3YeqKo/61wQgECQgJn5x9HIh9RgjRSDQBUCiMEqGKmkFwIIwV48gR2LCRSKwcXtUQEEggQINhAERTEInAhsmK8XJ0AgRwAxmCPE34chgBAcxlUYGiGAGIxQoswGBF6qquce5YIABAoIpB01HjyGCwKtCfjOrS+CjSAGg6CuK5biaviz7b+NN6/kCz3tmntKzIyFoDO3IwTb8qX2DQiwpWgD6DQZIfAxP2gRTJSBAAQgsA0BM/O8w98FW0cMBkGdip3F0PAo++8X3O6R9D2Ohqdr46pIACFYESZV9UGALUV9+AEr3iKAEGRQQAACEOicAGKwvoPSvMyj6kdXXK8z4qWnakMQ1vMRQrAeS2rqiABisCNnYMqJwAcEjWEwQAACEOifAGKwno/MzCPoP6yYZ9uNI9d2JRchBCuBpJr+CCAG+/PJgS16oqr+Y8gFAQhAAAIDEDAz3774TdBUtoleAGVmDyqsAl7nAgKxBQfnVDGEYAWIVNEvgSQG/UvUJ/1aiWU7J8CXy507mO5BAAL7JFAYgA4xeDYMCtnNHUAuBn2rqM/zuGYQQAjOgMYt4xFI0amI2jie60a3+AXbQUd3IfZDAAJHJlAoaBCDIlLIrMbw+khVPW8vVyEBhGAhMIpDAAIQgAAEIAABCByHQKGwObQYNDMPCvPVyqPDVwZv8uG1nDpCsJwZd0AAAhCAAAQgAAEIHIgAYjDv7JT384d8yTdK+PEJX817mv5fz+fo+QX934cFdT1XVc9HyFVAACFYAIuiEIAABCAAAQhAAALHJIAYnPa7mXki+Eh+QF/B80AyD1T11XW1JmHpK4y3giPuS1X1ermCBBCCQVAUgwAEIAABCEAAAhA4NgHE4GX/F2wJ9a2zd0q2cRZEcHWBeWNKXB579L7de4QgIwICEIAABCAAAQhAAAJBAojBt0GZma/svZNBOPv8pJn5tk/fPppr42tV9VVErgABhGAAEkUgAAEIQAACEIAABCBwIoAY/MNYMLO7IvJdKxF4xjzSzktVJUp88FFFCAZBUQwCEIAABCAAAQhAAAKIwTfHgJk9DuRrrpLiISjAq7R1hJGOEDyCl+kjBCAAAQhAAAIQgEB1AkFhcmp39tbI6oZXrNDMLFPdI1X11bzFl5l5VFEPSjO1RfRbVb23uLEDVIAQPICT6SIEIAABCEAAAhCAQBsCM8Sgr6Dt5fJtmJ9lOvNBSXCYHJgA72eq6uknuDIEEIIMEQhAAAIQgAAEIAABCCwgEBAnC2of+tbq+f3M7I6IfD9FRVXROIFhA6QAJIpAAAIQgAAEIAABCEBgigBi8CKdJts0c9tREYKxZxUhGONEKQhAAAIQgAAEIACBygTS6o6v8PhWvqvJyF+mlAGPVXWI7ZSIwbcGSLXzgec1B9JVEDAm8KwiBAOQKAIBCEAAAhCAAAQgUI9ASjng+d6uir/rGnFReF9VH9azok1NiME3uDY5r2dmnlPw1oQHP1ZVL8M1QQAhyPCAAAQgAAEIQAACEFiFgJl5cBFf3ftwZoPPRORuzeAjM+2YvA0x+DseVgRbDLBKdSIEK4GkGghAAAIQgAAEIACB6wmY2c201XMq9H8E4a++lVRVf4oU3qoMYvA1+epnBFMKiV+m/MoZwdioRwjGOFEKAhCAAAQgAAEIQGAmgYoi8GQBYnCmL1a+7aWq+ipwtYuoodVQCkKwHktqggAEIAABCEAAAhC4QMDMfPVu7nbQ65hWT03QwnlJBHtAnD1ekTyCVQO3BFZam5xL3KPzEIJ79Cp9ggAEIAABCAxOIG3/8slz1dWEwbGMar5HBJ0K7LGkX1+rqged4dqIQCCCZ7VzgumM6c+ZrjImgmMBIRgERTEIQAACEIAABNYh0GAb4TqG08oWBHyL6A1VfbVF47QpYmYe/OeTDIsqq4JrtnUE3yIEj+Bl+ggBCEAAAhAYiICZvShIKzBQzzB1gsAjEXlwCgCTPgb4Sl9OYHiVX6rqA+huQyByZk9EPP3HzSWCPaUc+S7Ty+pnErehuk6rCMF1ONMKBCAAAQhAAAIBAmbm2wh/CBSlyH4IfH5dfsDg5H+Is4L7cdfbPQl+vHmeor0Wr94GxwEfBQoHGUKwEBjFIQABCEAAAhBoRwAh2I5tpzX/L6r6b0/ZZma+2vdFxv73lqw2dcpmGLMKhJqLQc8DGU79YWb3ROSbAAy2CQcgnRdBCBYCozgEIAABCEAAAu0IpC2BP7ZrgZo7I/AnqvrfZITguyIymTdORD5W1aed9e1Q5gRXBU9MvhaRh6rq28AvXumjkG8PjgYaYotw4YhDCBYCozgEeiSQXpY9mnZ0m15M/cgdHQ79h8DEBLBFqgGA90fgn4rIvxpZyTMzF3lTggAhuLF/Z67mPxMR960/875l9GaKFOwRg98v6FKXKSNS9ONTn84jIL/u79YfLxCCBSOMohDojUA6oP1QRN7pzTbs+Z3Ak7QNpvhMBAwhcFQCaVXQ3221884dFWmv/f6HqvrXI8YhBCOUti9jZr6C99XKlviWUA9Ec+3q4pr2pPfXXT8PGXyH+XZZF8MeLGnVPiAE1xwZtAWBigQK9uNXbJWqZhJ4oqp7TSY8Ewm3QSBPIH3s8q/pXGMSyCUbD6/iBBLSsyLYyRgJJHyvaamLwNslZw5rNn5e14ytrJdM8RXS+2utFCIEW40G6oVAQwLk2GoIt13VH6z9pa9dV6gZAhCAQJ5AcKtgNshLMIl4lTx1+V5RIkJgJTHYhQhM49N3METPMkYQrrKbCCEYcQVlINARAURgR84oM4Wv1WW8KA0BCAxOIJ2PygV5+VZVPSrktVckibiqMqftbLw03ibqeQnvbL0SmHZneVTbFkd0XOh6H5sFQeKh6eyhwRwIZH4MfYuUvxBavHCA35YAQrAtX2qHAAQ6JBDY0ulWL80jyPb7Dn3vJqVVYV8tKwn8kuvNKqtlOSOCaU1y1UT+fu3zEbl5qgxCcClB7ofASgTSl1U/RIwIXIl5xWZequp5tLCKVVMVBCAAgX4JFOSA88m9n416nV8u7X7xwCOfBHrXbKIcaJsiGQJp/uKrvv5vyRxm1fNzU91aaevruQlNxjhCkMcXAgMQSC9RXwkkgt4A/rpiYhdnGMbDhsUQgMAeCKzwEZMPbYMMlDQWPJqm/4vOZ/w39HHKOdhsi2QJwg2D9X2qqs6i2oUQrIaSiiDQhkChCPQX5uuvqVxdEPAfrcmEuV1YiREQgAAEGhJofFasyUpJQxxU/duK77spvYIfefH/PkUH9lRLp5yCT7c+A3jVWWml+sdCJ57mZqd+nfpbGlymepoMhGChJykOgTUJFIpAN63616I1+0tbEIAABCCwTwLBs4KlnQ+nnyitmPIQuETAzPyITvS8o29l9dyA167ipdVFXyGNisKqYx4hyDiHQMcECveg81W0Y19iGgQgAIEjE2gQ8dqTcHv+OF9B4oJAcwIFK9vF0T5TzlQPqhM5Q1ltvocQbD5saAAC8wggAudx4y4IQAACEOiTQEUx6CLwbm/bBvukjlU1CBScdZ39gSI9Hy4Gc+cnq52LRQjWGB3UAYHKBApF4Jeq6jlsuCAAAQhAAAJdE6iQfNu323luNVYCu/Z03rgkrk5nA/M3bFvi3xGR/zRjwmwReKo3PR9+ljC3MljlKBBCcNtBResQeItAwdYDv/eRqvreci4IQAACEIDAMATS2ShPDxE9b+UJxD29hK+YcA1OIKUVcf/nBM9IPf2oxip1yr34Q6bjVeZ/CMGRhtfGtqaBeW7FqxoDfuNuddV8YUjiKi+BrgBgDAQgAAEIHIpAOht1J0WNvLolzldYfHXkce2w+YeC3FlnC+c6nVl/rTlfq6oL2yqXmXmAmakcmr+qqkcfXXQhBBfh2/fNacneX865aEaeBNZf0nylWzAkCl+MiMAFrLkVAhCAAAQgAIH1CRSctVvfuGUtfqCqHlG0yhVMU/Gxqi7KrYgQrOKu/VUyc8net23c46td+XgoFIHPVXWUPfXlMLgDAhCAAAQgAIFdEgisdI3Y73+gqn9U2/BAqorFMSIQgrW9Nnh96UuNL0dH85lc6jGrVQXjoDCK2uKDyAWmURQCEIAABCAAAQhUIZC2AX9fpbK+KvnvVPU/qm2SmXkgwC8m6l28HRUhWNtrA9c3I3n5VG8RLIGxgAgMQKIIBCAAAQhAAAJDE9jxllD3y+Itmpecm3bnfTPh+Ceq6ke4Zl8Iwdno9nVjZRF4gsPK4MQwKRSBnpz0BuGy9/Xc0RsIQAACEIDAEQjsdEvoyXWthOBtEZmKHvpMVb3M7AshOBvdvm4MLD/P7fDi/ctzG+75viS8PRJaJGy2i8DbRGjt2aPYBgEIQAACEIDAJQIFW0I9R2SPl0eznYrQiRDs0WvYFCMQzFcSq+ztUqxkXWFSuPqKCJw78rgPAhCAAAQgAIFNCRRsCV28zbFVR83MI3NOxc74vEXkfLaGtvIo9b5BIDDAz8ufvtb4l5Gr+X6uI/utqt4DuwgikFEAAQhAAAIQgMBRCAS3hPpH75s10y/U5Gtmnh7ts4k6m8xzA7v1CBZT09FHrMvMbojIz4G+e65ATw3xe46UdK8/HLkIo1WSXgZs7L6Imfl20KiAbvKFqXtIGAgBCEAAAhCAwPAECraEdn2MyMw8UfxXEw55qao+n656kT6iKk4qu0QgsOzst00GfQl+7fkzEfm/D+6FklVURODBBwvdhwAEIAABCIxKoGBL6OKAJ60ZBY9QVT0nGGzzo6XxIwgW03r0dF5/QMRlH9CCh71zGt2YhwjsxhUYAgEIQAACEIBAKYHA/NKr7HpL6HmfzeyViLwzwSE7Xy5hGNhBVmW3HUKwxCs7LBsYaCFRYmZ/W0Q+3yGitbu0eL/32gbTHgQgAAEIQAACEDgRCK5mefGut4ReEYK5c4LV+hPYiuptVUnRhhA8+HNrZpZB8EHk8K6Z/S0R+ZOD41za/SoP9VIjrrz4fM/7zfTv/E9+1vGnyNioaQ91QQACEIAABCDQL4GC9FhVV9BaEykQt6EFlOvsNbO7IvJdoD9VtqIiBAOk91wkIATfiyQxD0RU2jPGGn3rSgSmF5FHes0FtnkuIg9ahE2uAZU6IAABCEAAAhBYj0Ag0qUbM8yW0Csfx3NpJE7FZ610BuN2eBvVRDRCcL1no8uWAhGJPlXVxznjzey/EpH/JFeOv18k0I0ITF+8fPtDJNH9eWdeishdVfWXJBcEIAABCEAAAgcjULBqNksobY2zoH+vxVqaF/0ebf86+1O9Hpk0F4X/VEWV1UCvDCG49ajauP1ADsGQSAnU8/+3d/Ygd11dYl4LQmaKgO10IYVlUiYguUoxhaQ6heRuppJchEkTLNcpLHdJZQmmmM5SmWKw1KUYsARpAgFbRYqEgKUqKQKWAvmZBLLDks/1d3Xfe89Ze5/9e85zQJjve/fZP8/a597z3P3XuKXdFm8jarc8o66lW+Cck75UDdY4LhHi7xCAAAQgAIGNEdjqlNDTMDlHPI9vs+PXbEDFltPYspr3VwjBlt3cEpG7EQJot2Y9sxAR3NiDGNsc58v/7C8PEefExFZv6+mfTmcz2k5UTa/MU3tdPx40bTCFQwAC3ROImKLefVuoIAQg8J7AkFNCj2M3Ca/NflpaOlMi5NkHDxDBEmEaKM/pF4mfFqpsD66NWv3+S8bRLxr2S4ZNJZzbUteS/5mI/NeB0JSu6uteNlpx/hgQy2PVYunYwkgPAQhsi0Chz6VtQaI1EBiPwJBTQk8xT+/OJoNL7745I3TxXXxNIbsTwSl4drD3+4s1Te+Hp23+smdNmI1g2fC2jWDZbpK2s5FnPnO2Ra1rOjv3XiXg/CEgFZ1rx9nUzLkPAhDYJoHpF/dft9k6WgWB3RJ4pao2HXITV2UZLCKB7z1oE9GYacQ0bdHm4Nq/S8O4BtjM3iTnWQ/rtWrGJWKr2tRquTacSc2c+9IJFF7byQ8A6aHhTgjslkDkhgy75UTDITAYgc/PzSwbrA0fVHeSQXMHz2BKalNtOqhtxndlVl5qhsf3bVYEJ7mxHXhig2NSaEF9sCchdBwsn9rfkIFUcoXvi3zZssXOhw8h+0XvjrN6m/vgd7abZBCAQCKByM+mxFK4DQIQqEhgsxvJTTMYbImU970oBru9e5kEFttLYnMimNHOTQgfquqjmIiNmrbQEPfwi4JHjaen3iEE+8Fj6YPr7C9RU3+xD76lxdJ/JSJ/4anPhtPYTmHFPsQ3zI2m7ZRACMGWHvyy0+bTbAhsjcCmpoReCs40A9GcIXYA6lyW1Y7k2pQIRhzEGPOQFbfxmMqUTFtgiiijQSUDtjLvEEJYyGJ2d6qIraJX1nT423f1o9Lw0aIBXRBgs5guwkAlILCWQLG1bWsrVur+6V3au4fGaTXs7MEnqmo/tFe5NiOCmbe/P4WffbvWKtFNKGTqwPaLxpqdkOzBv8tGPAkBqHSLc+rV4oGlBX48qESgSTGb2C2tCTkK3SWB6cfdB5l+Yd8lQxoNgYYEbCDFZtYVWdvWsF2uoqeZDYc9SmyWw7nNFU38bMNG26fkRYvd5DchggmHO7qCeJJoTzJoa8A80/7OcbRObfOZrWNzdUrAcfajeypHCOH/isjf6rSpPVXrjaralwEXBCAAAQhAAAIQaE5geBF0vNDmhLyrg7IjN9wxUX5Uczg7Z2D3lpdj2tVjVbVf4hcvxxTTxTz2kkBVh//M3UusaCcEIAABCEBg6wSGfimZ1ijZyFPsNEYbtbLLRr5i793dUQjTxiB2cLzx+v0Mxuk8wcNw9i6H/kf9gMgsgv9LRP54VBY1640I1qRNWRCAAAQgAAEIzBEYXQRt+uI9Z4jtMHQbsfpAWKY5vLao00Y/PFJo69+usQugkzrJuiTgWNsXMzX0b0Tkb3fZ0L4qtasZBX2hpzYQgAAEIAABCJwSGFYEI7aXdh3EOI0u2iYpHrHc7HkoPCL7IMBmMdXjbFtB3+AHpOrcKRACEIAABCAAgQsERhZBz2hg9AYvzt1H2fSBR2p4Ao61fZ7jI5amZv9vEfl3w8Na1wA7r9G2g+YswXUcuRsCEIAABCAAgYwERhZBe6mam8qZPIXTedD27tYKZux3ZNUBAWc/Nxm0o0A+2AU24kB596YzHSChChCAAAQgAAEIQGA3BIYUQee0ti9Td7BcsQnNbjoODW1GINu5PI51gseNtHIP62tt06A7TgKfcZSIkxTJIAABCEAAAhCAQEUCo4rgQxH5ZobT6qmbzimiFUNFURD4nYCNdt/KcUhrCMFG+j4txPalqtphqlwQgAAEIAABCEAAAp0RGFUEl9YHrp6O5hx17CycVGdHBLLIYMF+bvWzzVE+mFK6o/jQVAhAAAIQgAAEINA1gVFF0M6uuzlDNsv6PcdmGl0Hl8ptnkAuGbTdcr/KTCt5anbmepAdBCAAAQhAAAIQgMAZAlsVwduqarK46kIEV+Hj5joEcsng0ih7TGtWj8jHFEZaCEAAAhCAAAQgAIF4AojgDDNEML5DcUcTArlkMMfI4NeqavlwQQACEIAABCAAAQh0TGCrIsjU0I47HVUrQiCXDN4VERO52A1k7MD0+zlG4ovQIVMIQAACEIAABCAAgQ8IjCqCdkDz3Pb1q6emFdxEgy4IgVIEcsngx3Z2oIg8EJHrC5W1cwYfpR7VUgoE+UIAAhCAAAQgAAEIzBMYVQRrHB+xNE2OrfF5uqoQiDzvz0bmbLfOtzkqF0K4ZvlN/46ztDMFf2ZX0ByUyQMCEIAABCAAAQjUJzCqCNrZZD8u4EretdB5oPzqUcf64abEUQlEyqCN0tk5g1lkcFRm1BsCEIAABCAAAQhA4DKBIUXQmhNCsJfcj2aCa9PkrqW8DIcQlqaeWrFZ1iHSOSHgJYAMekmRDgIQgAAEIAABCEBgicDIIujZ7j56ZCSEYOuivlsA905VbR0VFwSqEkAGq+KmMAhAAAIQgAAEILBZAiOLoK1d+sURGZPBu0trmabpoLYu8J4jz29V1dYpckGgOgFksDpyCoQABCAAAQhAAAKbIzCsCFokQgieUcFD0B5Puxu+Po7iJIC2Q6KJnWfL/OQpp5vrPTSoGQFksBl6CoYABCAAAQhAAAKbIDC6CNqooO1eOLdW8DRQJnJ2j102vXNpe/zT+zkwexNdf/xGIIPjx5AWQGS/LncAACAASURBVAACEIAABCAAgVYEhhZBgxb5MryW83NVtdFDLgh0QSCE8G9F5E+clYleM+vMl2QQgAAEIAABCEAAAoMRGF4EJxmMmSKaGiJeolPJcV8RApFTow91oB8XiQaZQgACEIAABCAAgbEIbEIEK8ggL89j9evN1zZRApHBzfcMGggBCEAAAhCAAAR8BDYjgpMM2oYv3/ia7k71ctp1lMO53chIWJLASglEBksGh7whAIHsBEIIth/AfRG5Na3xf6aqL7IXRIYQgAAEdkZgUyI4yaB9UdhUUc8OoEvh5piIJUL8vSqBTBKIDFaN2tXCQgg3ps2qpOUL7bRrstXFrtdLx+zUwNaaTQflm/TYP7t+VtVmP0KGEOz71I5VOmyq9kZEHqjqs0p9wTZ0+xci8s9F5I9OyrQfaZHBGoG4XMZxX7V+etiIr22t4kq3zz17Z+SCwC4JbE4EJxm0Lw87GN7+xewoeugET+04iR5einbZK2n0WQKZJRAZbNDPJvGyl+ibR8Xby7WddVr1JSqEcG4GRbMfv1qzaV3+9N3VTUwmIf7pwmPyRWkZnMo30Uv5Dm/wdFPkwARY/jNw8Kj6OgKbFMFjJCEE2+XT/tkvm3OjhPbror2gPWn5C+y6cHL3VglESqD9kGHXPScPvgSdoNYmCyGY7J07sqbq+aQLuy03OSKnNZsOyrcfLr+70Me+rD1qEUKw78M7F+rzSlUPI8lrH4tLP3zZmb85ZvYUqR+Zbo4A34POkE4/mtk7tf07/RywkWH7AedF7R83ndUn2QmBzYvgiRTaSOFpp+1iOhQ9EwKXCERK4O9fZrH3lX6x23uEp2l2P85wqCZgIYS5l+w3qnqYmlglbK3ZLIx+GYPisekwJmEu+Kpa7P3B0R+q9EsK2R0BZHAm5JMA2qwFW6/rGak3nja7rspU8t311kwNLvZBnql+ZAOBXROIlTn7he54RDvy/qeqah/wXAUIXJiKeVxStWmZIYRmL/nn0LZmE0KYG42zKhePTYcxadZHEMECH0Bk6SWADJ4hNc0isfXCHgE8zYFNF729r0E6RLABdIrsh8DRFIei05wSW2x1ujQ16zTLi19ekTJo+TTbnCKRU++32XRQ+0XUptHM7WpcXDYOoDqUjqUdn4uyaS2iFpcOY9JSBO2z79L6xN6fd+o3PgFk8CiGke8Ql6Jvyx/sh+qqa+HH74rlW4AIlmdMCZ0SWPkLV0+tWvzSyvRB3lObR6zL8wWxLyo7x8A6lA5EsL9R2mYiOInxpfW0Iz771Hk8Aovfq+M1Kb7Gmd8dkMH4EBS/AxEsjpgCeiSwoalH7i+rzB/oPYZ19DohgpcjWJQNI4JXwbf+sWCarfHvReQfjP5gU/9hCTxXVdtscJfXwqZiqUxsl+wbbMqYii//fYhgfqbkOACBjUiRWwIPIdlIuwfoYUlVLCo7jAhejgki2J8IWo0cP9jZSyVnwCV93Ky6aW6Ku2X87arcy98cs+xil2vnQwi2YZiNyqesCVyK4C6ZLkFp9XdEsBV5ym1KIIRg2xsfn+XWtD4JhUdLIDKYQLnuLYjgZd5F2SCCw4rgS1W1tbdcFQm0Hi3O0dTIH0V3Jy6RfFJC8hlndadgy38PIpifKTkOQGBwEUyWQGSw685ZVHaOW97bi1xrEWtdvsWmw5g0XSM4MTHJmztyBRFs8JHWW19NRRApO7uRwWk08BcHV1vzZyPyh+Mh7Ig224HZ8yP7bng6ODZNggg2xU/hrQgMLIKrJfBIBucOjG4Vmi2X+zci8kczDUQEGRG8SKDkuX3nCu3hZd8xNRQRbPCJ2UPfyNVsZPAqScdxOnbT3E7ldgzV9wsxeqeqJo5cjQkggo0DQPFtCAwqgtkkcPq13T6E7dc87xEVbYK1jVJtLdO/EZE/RwTPvniwayi7hl7pGIhgnx9+WxLB6bvQvgfv9Um7y1rZSOC1uQ1fnDJ5W1VtmQ5XQwKIYEP4FN2OgEMEn4rI63Y1vFLya1UtsinCNA3EFoZzFSJgX3Y9TD88NK+3F7nWbFqXP72MNp+Kedz9e+gjiGChD6SV2fbQN1Y24dyPDsigH+qXnveREIK9Q33aw4+f/qbtLyUiuL+Y0+Lf1uMsbRbDL1X0lKwEepANRPB8SHuITW8v1z3UBxHM+hGULbMe+ka2xkwZTceV2HvB9dx5bzC/TzzHP4QQHonIV4hg3z0AEew7PtSuEAFEsBBYsr1IoAfZQAQRQe8j2sPLPiLojVbddD30jRItRgZ9VL3rlR3nEO76nEYf7fKpEMHyjCmhQwKIYIdB2XiVEMHLAW7NpnX5Rqa3l+se6oMI9vmh2EPfKEUGGVwmGyGCS2u/H6uq7TLK1ZAAItgQPkW3I4AItmO/15J7kA1GBBkR9D5/PbzsI4LeaNVN10PfKNliZHCR7ueqaofNz14hhKWdyavtlL1U1z3/HRHcc/R33HZEcMfBb9R0RJARwbmu19vLdQ/1QQQbfVgtv+B3tbFRCUrI4CzVxSmdzrMIXZvOlIgvef6BACJIb9glAURwl2Fv2mhEEBFEBOMeQUQwjlet1D38SFCjrZMM2oYnd0XkoxplDlTGRYmLkOjPVLWn3dkHwp+vqohgPpbkNBABRHCgYG2kqoggIogIxj3MiGAcr1qp9yKCtXj2Vk4IwcT3B0e9HovIw+MdRKdn1o7imDs2wrJ+pao3HGWQpDABRLAwYLLvkwAi2GdctlwrRBARRATjnnBEMI5XrdSIYC3S7cpxnAF4XLmX0/+w84iXBPBwH9NC24X3g5IRwU4CQTXqEkAE6/KmtPe7Qi7toFZt4XxvL3Kt2bQu356PDmPSfB0YItjnJ2dvfbVPSmPXynH0w5oGvlFVk0auDggggh0EgSrUJ4AI1me+9xJ7kI1DDHp7kWvNpnX5iOD5TwdEsM9Pzd4+P/qkNH6tHO9JqY28raovUm/mvrwEEMG8PMltEAKODzg+qAaJ5SjV7EE2EMGLwtF8tLa3l+se6oMI9vnp1kPf6JPMtmo1bfpix0R4p3t6AFSb+eKpDGlEEEF6wS4JIIK7DHvTRiOCl/G3ZtO6fEYEGRFs+uEUWTgiGAls4OQhBNvQxUbvcuya+lRV7w+MY5NVRwQ3GVYatUQAEVwixN9zE+hBNhgRZETQ2697eNlnRNAbrbrpeugbdVu879KmMwHtcPjrK0h8rap2FAdXZwQQwc4CQnXqEEAE63CmlD8QQAQv94bWbFqXb2R6e7nuoT6IYJ+foEt9Q0Q+V1WbUsi1IQLT5+SDyNFB21H0Af2h346ACPYbG2pWkAAiWBAuWZ8l0INsHCq29CKnqlW/G1qzaV0+Inj+QwMR7PPDdOnzQ0TeicgtXv77jN+aWk3rBm16p501ePNCXhZ/G0F8wqYwa2jXubfql32dJlEKBJYJIILLjEiRl0APsoEIXhQONos5QbP0sl/jxwJEMO9nUK7cQghvHaNCyGAu4B3nMz2jxzV8raqvO64yVTshgAjSJXZJABHcZdibNhoRvIy/NZvW5TMiyIhg0w+nyMJDCDbac8dxGzLogEQSCLQkgAi2pE/ZzQgggs3Q77bgHmSDEUFGBL0PICOCXlL7S+cYqT2GshkZnDZNsYPQ3zLtdX/9fqstRgS3GlnaNUsAEaSD1CaACDIiONfnehCv4/r1UB+HcLxU1Vu1n2XKe7+5ka0T+97J4o2I3FBVm1I6zDWJn22OYn3s3I6Z1i47WoG1cMNElYqeEkAE6RO7JIAI7jLsTRuNCCKCiGDcI4gIxvGqnTpSBl9NG8h0L4OTANq64XsRTG13zIdsjhJBjKRdEEAEuwgDlahNABGsTZzyEEFEEBGM+xxABON4tUi9NRmc2mPn3aUeoP5YVW0UkQsCQxBABIcIE5XMTQARzE2U/JYIIIKIICK49JR8+HdEMI5Xq9RbkcHIdszhZspyq85IudEEEMFoZNywBQKI4BaiOFYbEEFEEBGMe2YRwTheLVNHSlR300Qj6+9B/VRVbR0lFwS6JoAIdh0eKleKACJYiiz5XiKACCKCiGDc5wMiGMerdepImepGBkMIN0TkpwL8vlZVm2bKBYFuCSCC3YaGipUkgAiWpEve5wgggoggIhj32YAIxvHqIfWIMuh4H0hFa0dn2G6pHLCeSpD7ihNABIsjpoAeCTg++G+z+1ePkRu3ToggIogIxj2/iGAcr15ShxBss5TvnPVpOjLo6GOnzbD62lmC3s1kmCLq7Agka0MAEWzDnVIbE0AEGwdgh8UjgoggIhj34Dte0tmUIw5ptdQhhCcRxy80k8EQwjMRubMAxkb2Hqiqten9NfVNm/Z57nzB4+zeqerH1cBTEAQiCSCCkcBIvg0CiOA24jhSKxBBRBARjHtiEcE4Xr2lTpDBlGMXXq+ZehlCCA4JvDi90ymSX6iqCScXBLojgAh2FxIqVIMAIliDMmUcE0AEEUFEMO4zARGM49Vj6kgZ7LEJs8tEQgg22mdrAOemin6rqnZAPRcEuiOACHYXEipUgwAiWIMyZSCCvj7QWpJbl2+UlkYmVLXq93UP9UEEfc9P76kGlsFXqmo7is5eIQSbIvrVTCKmMC9B5O/NCFT9YmnWSgqGwAkBRJAuUZtAD7JxaHMPL/k9SXIPsekwJrNT5mqIKSJY+1OqXHmDyuBjVV2crko/LddvyLk8AUSwPGNK6JAAIthhUDZepR5kAxE838l6iA0ieDU2vGBv60NxQBl0Temkn26rn+6tNYjg3iJOe98TQATpCLUJ9CAbiCAi6O33PYgpL9jeaI2TbjAZdE3pdByX4cpnnChS0y0RQAS3FE3a4iaACLpRkTATAUTwMsjWbFqXP/041Xwq5nGEEMFMDz7ZXCEwmAx+trQraQjh54VjJFxTTOkqEGhBABFsQZ0ymxNABJuHYHcV6EE2GBFkRND74CGCXlKkSyEwkAzOjuY5RgMNz5fHZxCm8OIeCJQigAiWIku+XRNABLsOzyYrhwgyIjjXsXsQL0YEN/nR022jpqm/d0VkcWfOmUbYvXNHN+Ro/3MRua+qb0+eETsS4htHAYujio48SAKBIgQQwSJYybR3Aohg7xHaXv0QQUQQEYx7rlkjGMeL1PEEQgj3ReR7550mhDYN9JqI3BKRTx33PVdVk10uCHRJABHsMixUqjQBRLA0YfI/JYAIIoKIYNznAiIYx4vU8QSmA+FN7jxSF1+AyOyB9CkZcg8EchJABHPSJK9hCCCCw4RqMxVFBBFBRDDucUYE43iROo1ACMFG7H5Iu3v2LkYDC0Aly7wEEMG8PMltEAKI4CCB2lA1EUFEEBGMe6ARwThepE4nUGDzmje29vF0XWF6DbkTAmUIIIJluJJr5wQQwc4DtMHqIYKIICIY92AjgnG8SL2OgOMYCG8B72wNoaralFMuCHRNABHsOjxUrhQBRLAUWfK9RAARRAQRwbjPB0Qwjhep1xGY1gs+EpF7K3J6Ne0wigSugMit9QgggvVYU1JHBBDBjoKxk6oggoggIhj3sCOCcbxInYfAtJOoCWHssRSPReQh00HzxIFc6hBABOtwppTOCCCCnQVkB9VBBBFBRDDuQUcE43iROh+BaXTQjpZ4sLCjqE0DfTYJ4Ot8NSAnCNQhgAjW4UwpnRFABDsLyA6qgwgigohg3IOOCMbxInUZAiEEOzfwcHbgoRCTvp9ZB1iGObnWI4AI1mNNSR0RQAQ7CsZOqoIIIoKIYNzDjgjG8SI1BCAAgVgCiGAsMdJvggAiuIkwDtUIRBARRATjHllEMI4XqSEAAQjEEkAEY4mRfhMEHNtE31bVF5toLI3oggAiiAgignGPIiIYx4vUEIAABGIJIIKxxEg/PAHnwbGI4PCR7qsBiCAiiAjGPZOIYBwvUkMAAhCIJYAIxhIj/dAEnBJobUQEh450f5VHBBFBRDDuuUQE43iRGgIQgEAsAUQwlhjphyUQIYHWxk84C2jYUHdZcUQQEUQE4x5NRDCOF6khAAEIxBJABGOJkX5IAo6X8ON2PVZVOzuICwLZCDj64Leq+jBbgTMZhRDCXDmqWvW7oTWb1uVbLDqMSfM+ggjW+DSgDAhAYM8Eqn7Z7xl0z22fzsi5KyL274aIfHRUXzss9efpwNRnqjrcgakhBDsU9ntnDF6JyC1GA520SOYm0INsHCrboXSYAH8zA7OoJPcQmw5jggi6n24SQgACEBiTACI4Ztyy1HoSQHsBuxeR4VMReTCKKCGBEZElaVECPcgGIng+xD3EphcRnL4XbEbEVwsPxHP7gVBVn5R6cBgRLEWWfCEAAQj8RgAR3GlPmATp0cnon5eGjRLeV9Vn3htapEMCW1CnzEsEepANRBARnOmfH4uIfSfE/DBo2b2ZfhzM/n2ACPJ5CgEIQKAsAUSwLN8uc4/cNGWuDV+W/DV4DTwkcA097i1BABG8TLU1m9blG5mWI4IhBFsSYOemHi8LiH0MbITQfiB8G3vjjJzeEpEfZ/J7qaqWhgsCEIAABBIIIIIJ0Ea+xfHCE9u87mQQCYwNIelrEHA8e0XXwR23saV0nGPdmk3r8luKYOTn5dKjknWNNSOCS7j5OwQgAIF1BBDBdfyGutvxpZrans9V1TaUaX5FvtRkfWlp3ngq0DWBHmTjAAgR/LCr9BCbFjGZRgJ/yvzgvFJVG2FcfTm+sxgRXE2ZDCAAgT0TQAR3FP0Qgu34+WmBJmf74l9TN8dLw3H2ts7xxoi7oK5hxL3tCPQgG4jg+fj3EJvaIhhCsDWB9p2wZjropQcqyxE8js90RLDdRxolQwACGyCACG4giJ4mRI6UebI8TdN0imjkGheTQDsiootRzBTY3DMegR5kAxFEBI/6gG0Ms7Qz6JoHbfVMEURwDX7uhQAEILBMABFcZrSJFM7RQBMk2zbctgR/O/1ibP/b/i39atzsl1kkcBNddPONQAQvh7g1m9blG5maI4LTERG/OB+6/yMi/29K+z9F5O8673uuqnY2bfKFCCaj40YIQAACLgKIoAvT2Imc60AurpeLEK1Pcu4Y56EeUTfLjpFAD1TSFCHQg2wcGlZTOjwwW7NpXX4DEbTzY79ZiI19Xlq6J8ef65NE2mjiHUdsP1sz/R4RdBAmCQQgAIEVBBDBFfBGuTWEYCN63y3Ud3Yaj+ML2bL/DyLy3yK52DqV65H3pCRHAlOocU82Aj3IBiJ4Ppw9xKamnIcQbFr83Ofu4uel8xiir1XVpDHpcnzvNJuJktQgboIABCDQGQFEsLOAlKiO4yXnqareXyrb8fKwlEWrvy++1LSqGOXuh4DjOeT4iMvdoSibHmJTWQTDwpPn4u1YcuD6brlUF0RwP5+PtBQCEGhDABFsw71qqSEEOyj45kyhrl9tHS9LVdvlLAwJdIIiWVkCjufH9fKdo5Y1pcNT39ZsWpdvjGrFxCFXVh3XNH/HJmSrRuwcdV2Vv6dvkgYCEIDAlgkggluO7tQ2hwh+oarPllA4XpaWsmjxd1fbWlSMMvdFwPH8IIKXu0RRNj3EpiMRdMtVaVErnf++PoFoLQQgAIGrBBDBHfQKx1oO10tWCKH0duO5o9H0SIvcjSG/sQn0IBsHgrWkwxux1mxal9/ZiCAi6O24pIMABCAwOAFEcPAAeqrveMl5o6rX5vIqfPiwpxmxaZDAWGKkL0rA8Ry6fpDJUUlE8EOKPcSmVkwco2wGh6mhOR408oAABCDQOQFEsPMA5aheCMHOcvphIa/ZdYKDjQYigTk6DnlkJdCDbDAieD6kPcSmlgh6Rh9FxPWjhGOzmFVnCTqk1T16mfVhJjMIQAACGyGACG4kkI7RvF8dTT0rgwNJoJ2F+EBVbXMcLgh0RaAH2UAEEcFJBJeOj7BkS0cKPRGRe2t+YFx6QBHBJUL8HQIQgMA6AojgOn7D3O1YJ3hoyxsRsY1j3oqInfFno4mfOhr6VyLyF450V5IgbinUuGc0Aojg5Yi1ZtO6/EnOZo90UNVs39fOs2WtWt+KyCMOlB/t04b6QgACEPARyPbF4iuOVK0IhBBsDeAvBcv/TFVfF8yfrCEwNIEeZOMAsOY0RE/QWrNpXX4DEYz5PrAjeGwE0T7f7b65o4iOw71qWujE5JaI/DjTh5ga6nnASAMBCEDgAgFEcEddo+AUT9d6kh2hpqkQuEKgB9lABM93zB5iU1vOC34fHCDfXjvbg6mhfJBCAAIQKEsAESzLt6vcp50/bf3c9YwVe6WqNzLmR1YQ2CSBHmQDEUQEj/qATf23kT7P1P/YZ/Kxqj6Ivek0vWMKKyOCayFzPwQgsGsCiODOwh9CMGkzGfwoQ9Ntc5Zbx+tHMuRJFhDYJAFE8HJYW7NpXb6RqT0iOJWZ8/vgEOAs3wshhPsi8v3ChwEiuMlPSxoFAQjUIoAI1iLdUTmTDNqOb2tGBrN82XeEhapAoCiBHmTjaDSo2sYkHqit2bQuv5UITuXaOjzbIKybHwcdI4GHbrV6HaKnf5IGAhCAwFYJIIJbjexCu6Zpog9F5KsEBI9F5CEjgQnkuGW3BHqQDUTwfPfrITYtRgSP+oONDJoMrpkm+tJ2mV77vRCxw7VVf/b8291+2NBwCEAAAk4CiKAT1FaTTbuJmhAunQdlO8fZi4IJILuDbrVD0K5iBHqQDUQQEbzUwVf8OGjfDXZ+q80yWXVFSqAddXRjrXiuqjA3QwACEBicACI4eABzVn/aoc2mCR1fdp7gz2t3f8tZT/KCwIgEEMHLUWvNpnX5RqbliOBxZKYfB219nv2bGyG0EUCTv2c5ZCxSAk0+bX26bXbDBQEIQAACiQQQwURw3AYBCEAghkAPsnGoby/ScVQfm5XwzQzPokfU9BCb3mIyyantLGrTRg//tY3GJPcPg5ESaOvTbQoqM1NiPoBICwEIQOAMAUSQbgEBCECgAoEeZAMRPB/oHmLTowhWeCxsJNRGFZeWJhyqwiZlNYJCGRCAwG4IIIK7CTUNhQAEWhLoQTYQQUSw5TNwWjYS2FM0qAsEILBHAojgHqNOmyEAgeoEEMHLyFuzaV2+kdnbiCASWP0jiAIhAAEIXCGACNIpIAABCFQg0INsMCLIiGCFrr5YBBK4iIgEEIAABKoQQASrYKYQCEBg7wQQQUYE556BvYwIIoF7/ySk/RCAQE8EEMGeokFdIACBzRJABBHBvYsgErjZjzcaBgEIDEoAERw0cFQbAhAYiwAiiAjuWQSRwLE+r6gtBCCwDwKI4D7iTCshAIHGBBBBRHCvIhgpgf9JRP5xjkPqGz/yFA8BCECgewKIYPchooIQgMAWCCCCiOAeRTBSAg3RfxeRm6r68xaee9oAAQhAoGcCiGDP0aFuEIDAZggggojgGhEUkaeqen+kByJBAg/Neycit5DBkaJNXSEAgREJIIIjRo06QwACRQiEEG6IyEN7CRWRj4oUcjnTb1XVyi5+9bZDZWtJbl2+BXwpJlOnGEIGQwgfi8gLEbm+ojMjgyvgcSsEIAABDwFE0EOJNBCAwOYJhBBstOX7hg1FBBtJcici+Nb548NLEbnb6xq6TBJ46AnIYMMPJIqGAAS2TwAR3H6MaSEEILBAoAMJtBpWG+1ZGn1S1arfDa1FrHX504jgExG553xYX01TJ00eu7kySyAy2E1kqQgEILBVAlW/7LcKkXZBAALjEuhEAg3gS1W1KanFL0TwQ8SdiGDsdMquZDBBAn8Skc+dnZ2RQScokkEAAhCIIYAIxtAiLQQgsCkCHUkgIijyzUznKjpttgcRtLYnyJTJ4P3Wm6ok1Pv96Hfk84cMburTl8ZAAAI9EEAEe4gCdYAABKoTiHwJrVG/56p6t0ZBjAj2NyJ4qFGCVDUVpIT6fjAFOvI5tLaa+D6r8ZxQBgQgAIGtE0AEtx5h2gcBCFwhEPnyWYtg0VGv40Yggv2K4NHIoMnOTWfnayaDIQQ778+7O+jZdbAJz+OXqmprKrkgAAEIQGAFAURwBTxuhQAExiOQ8NJZq5GI4GXSRdn0MjX0tPmR5/CZDNpuonZsQ5Ur8lma3QwpMi9rHzJYJcoUAgEIbJkAIrjl6NI2CEDgAwKRL5tZR1l6kg1GBD98MHqKzUoZrCpIIQQbtbzj+Jhx7Ygb+XxasZ+3Xh/paDtJIAABCHRLABHsNjRUDAIQyEkg8iUzqwRaO3qSDURwHBGc+s4jEfkq4nmoMloWQrDRx6Xpqy4JPLQthGA755pgfuRob7V1tY66kAQCEIDAcAQQweFCRoUhAIFYAiGEmBfp7BKICM5HrLUkty7f058jf8iwLIvLoEMEoyTwSAZviIhJ5pIMvlNVO3aDCwIQgAAEEgggggnQuAUCEBiHQMI6q1slppv1JBu9jAhOO07aS//9hcPUd7lG8PQpS5DBJBHzPt0OEbydumYxhOCSQVXlPcYbMNJBAAIQOCHAByhdAgIQ2CyBXiSQEcE/dLFJ/kz87J93t0lEcELYkwyWFMHpmTEZtIPnL16I4GY/vmkYBCBQgQAiWAEyRUAAAvUJ9CSBiOBv8Z9GRR84pvyddpiX046Yb0v0pJ5Gaz3t846WHeVVZGSwtAhOfSYggp5eQRoIQAAC8QQQwXhm3AEBCHROIFICX02S8bpks3qSjdpTQ0MI16YNQLwjgOdCYXGyw8Tt3LqsV0+x8TYsQQazyzQi6I0W6SAAAQj0SQAR7DMu1AoCEEgkkCCBtiawyEjTcRN6ko2aIpggLHOR3/xGPjHdPoGtyXS2/o4IxkSLtBCAAAT6I4AI9hcTagQBCCQS6FUCrTl7FMFpPaCNtC7t/hgTcZPBG6qabQS3p9jEgJj6VexoazYZRARjo0V6CEAAAn0RQAT7ige1gQAEEgn0LIE7FkHPOXMpEX+lqraRSJZrZBGc+pYdoWCsvVNv30zToVdNs0UEs3Q/MoEABCDQjAAi2Aw9BUMAArkITIdQ/+jML9uIqNpQxwAAIABJREFUiLO898l6ko0aU0MTdreMwWlpv1ZVOx9y9dVTbFIbM42+xsjg6mm2iGBqtLgPAhCAQB8EEME+4kAtIACBFQQcL/KH3JtI4E5F0KZufuoMq21k8ndE5B+KyB8778l2mLij/xQ9vsLZ3sVkkww+EZE7i4l/S7BKBhFBJ2WSQQACEOiUACLYaWCoFgQg4CfgnBbaTAL3JoIhhLsi8oMjgo9F5OFhs55JZP6JiPxTEbnpuP9LVTXxWXVtRQQPEJzPwyG5yeC1lA2TEMFV3Y6bIQABCDQngAg2DwEVgAAE1hJwvJA2lcAdiqBN2fxqIa6zEuecWvpcVU06V11bE8Gpv5kg33OCSZpm63jubquqTVdNvmpMY06uHDdCAAIQGJwAIjh4AKk+BCDwfv3d0qYkq19I13LuSTZKv1yHEGwTkrmNS1wC5xjZeqOqtmvmqqun2KxqyMnNIQSPkNtdSVNfazx3pftqTt7kBQEIQGA0AojgaBGjvhCAwBUCNV5I12LvSTZKv1wv5S8in3sOhp8Oov9ljr2qrv4e6yk2a/vZ6f3OkVVEMDd48oMABCAwAIHVX6ADtJEqQgACGyeACMYFeEnU1spVzvxz5nWJ0pZF0NrsGBlEBOMeIVJDAAIQ2AQBRHATYaQRENg3AUQwLv6l5Spn/jnz2rEIPhSRb2Z6CSIY9wiRGgIQgMAmCCCCmwgjjYDAvgkggnHxLy1XS/mLyBeq+myp1kwNXSLk+3upEc8az91SX1o7eu0jSCoIQAAC2ySACG4zrrQKArsiUOOFdC3QUi/jKfUq/XLtiMdLVb21VHc2i1ki5Pt7qb7niPPqTZpK91UfQVJBAAIQ2CYBRHCbcaVVENgVgRovpGuBlnoZT6lX6Zdrx5o0q/bsdETnJidPVfV+CoPje3qKzdq2nLu/VPtqPHel+2oJ3uQJAQhAYBQCiOAokaKeEIDARQI1XkjX4i/1Mp5Sr9Iv1yEEG+370VG35yLyQFVfH9JOh8o/WFjTdkjOgfIOyKX6Xo3nrnRfdeAjCQQgAIHNEkAENxtaGgaB/RCo8UK6lmapl/GUetV4uQ4hmNx96qzfKxF5O6W96bznnap+7Ew7m6yn2ORoz2kepdpX47mr0VdLMCdPCEAAAiMQQARHiBJ1hAAEll7kOVA+oo/UeLkOIdwVkR8iqhWb9GtVtQPTV1+lRGl1xTJlUKp9iGCmAJENBCAAgUYEEMFG4CkWAhDIR6DGC+na2pZ6GU+pVw0RtHqFEGxn0DspdVy455Wq3siVb0+xydWm43xKta/Gc1err5bgTp4QgAAEeieACPYeIeoHAQgsEqjxQrpYiYUEpV7GU+pV6+V6Wu9no7XXU+p54Z53InLjeF3h2rx7is3atpy7v1T7ajx3tfpqCe7kCQEIQKB3Aohg7xGifhCAwCKBGi+ki5VABM8SyCyDJoG3VPXntfE4vr+UKOWs45q8SrWvxnOHCK6JPPdCAAIQmCeACNJDIACB4QnUeCFdC6nUy3hKvWq/XE8y+GTlNFHbUOZ+bgk0fj3FJiWeS/eUal+N5652X11iyd8hAAEIbIkAIrilaNIWCOyUQI0X0rVoS72Mp9Sr1cv1dDbgw4jdRK15Ngpom8I8UtXDzqIpzb54T0+xydqwKbNS7avx3LXqqyXiQJ4QgAAEeiOACPYWEeoDAQhEEwghmCB8NHPjZznXlEVXsLNRp9Yv15MQ2q6icxvJ2AigjSI+KSWAhziWEqWUflLinlLtQwRLRIs8IQABCNQjgAjWY01JEIBAIQKtxcbTrFIv456yT9P0xCuEYLt/np4H+HNp+Ttm0lNsUuK5dE+p9iGCS+T5OwQgAIG+CSCCfceH2kEAAg4CPYnNpeqWehl34LmSZAReKe1Kvaen2KS2Ye6+Uu1DBEtEizwhAAEI1COACNZjTUkQgEAhAiOITamX8RSkI/BKaVfqPT3FJrUNiGAJcuQJAQhAYNsEEMFtx5fWQWAXBEYQm55kYwReNTtuT7Ep0e5S7WNEsES0yBMCEIBAPQKIYD3WlAQBCBQiMILYlHoZT0E6Aq+UdqXe01NsUtvAiGAJcuQJAQhAYNsEEMFtx5fWQWAXBEYQm55kYwReNTtuT7Ep0e5S7WNEsES0yBMCEIBAPQKIYD3WlAQBCBQiMILYlHoZT0E6Aq+UdqXe01NsUtvAiGAJcuQJAQhAYNsEEMFtx5fWQWAXBEYQm55kYwReNTtuT7Ep0e5S7ethRFBEbpdg1iDPt6r6c4NyKRICENgxAURwx8Gn6RDYCoERxKbUy3hKDEfgldKu1Ht6ik1qG/Y6IliCV8M834jIXYSwYQQoGgI7I4AI7izgNBcCWyQwgtj0JBsj8KrZT3uKTYl2l2pfJyOCJZC1zPOdiNxCBluGgLIhsB8CiOB+Yk1LIbBZAiOITQjhiYjcmwnCt6r6sEaQRuBVg8OhjFKiVLMNjAj2QjtLPZDBLBjJBAIQWCKACC4R4u8QgED3BHoXG4cEGmNEsFFPQwTT+h4jgkU7LDJYFC+ZQwACRgARpB9AAALDE+hZBJ0SiAg27IWIICLYsPstFf2lqtpsAi4IQAAC2QkggtmRkiEEIFCTQAjhvoh87yjzuYg8UtUXjrRZkkRIoJVX7YWvZ3HOAj4yE0SwaxG05/VmZEi3lrzaZ8PWwNEeCEBgngAiSA+BAASGJBBCuGtiJyKfRjbgpYg8KL0ZQ6QE2m6BN1T1bWRbkpIjgh9iQwS7FsFrImLHKnyU1Nm3cxMyuJ1Y0hIIdEMAEewmFFQEAhDwEoiUrEvZFnuxiqxf9S3jEUFE8OShSFqfWmONoNUzhPCxiNjIv/34s7UrZrSz2GfW1qDSHghAwEcAEfRxIhUEINAJgUjJWqp11her6YXV1vPcWSp4+vuraav4KiOBhzohgldE0EaWv5qJWZIoOftA8WSlRjxriWBxQA0LmKa2W//zjnhm/cxq2HSKhgAEOiCACHYQBKoAAQj4CGSWwEOhWV6sJgm09UzXfa2RJhJodUME/xAh5xpTRPBMp0YEnU/6QrIQwg0Rsc8OZDAPUnKBAAScBBBBJyiSQQACbQlMawJ/KFAL26bd1ue9Ts17JAlEBKMl0G5ABBHB1I8H130JMvi1qtpIIhcEIACBZAKIYDI6boQABGoRmETLRM37i3ls1Z6ratL6o9EkEBH8rWs4RwIP/Wjol26mhsZ+HLRJnyCDT1XV1k5yQQACEEgigAgmYeMmCECgJoHIl3bbFdSmWdkGE7cipmp+FjsqOKIEIoLRElh1R9cSzxUiWIJqmTyRwTJcyRUCEDhPABGkZ0AAAt0TCCHY9vFLa+9MAO+fylwIwWTQNnBZOmbisao+iIERuWbx6XRsRdWNYc61Z89rBCN/VLBpw7dKHzUS0+dS0iKCKdTa3YMMtmNPyRDYGwFEcG8Rp70QGIzANOr260K1X6qqCd/Zyzm19JWq2qYN7iuEYNNVlwTT8utqCtdeRXCPEmidDxF0P9LdJEQGuwkFFYHApgkggpsOL42DwPgEphG9Hxdasjit0yMBqhr1mbgkVFOdu5LASQzCHM9YDiP0MocMHTdjEyOBhwY52p60GQ67hpbt+ZMM2myGpdkQh4p091lTlhC5QwACawlEvfSsLYz7IQABCMQSCCHYdM3vZu6bHQ08vs8hbp/HTAN05Nfli9lSvbcmgpFTeDclgZP4PxSRb2aeIUQw9oOpUvqEdchdfuZUwkUxEIBAJAFEMBIYySEAgboEco5m5B7BGFWoRq13Ss+LlEDbGOZuzI8BKXWqfU/OZ+jkhxXblOnmTHtuq6ql4VpBIEEGo9c7r6get0IAAgMTQAQHDh5Vh8AeCDheYt2/gIcQbKOWuSMool5cRxWqUesd298jJfDVtDFM8818Ytu5lN7xDDEiuASx8d8TZHBxunzjJlE8BCDQAQFEsIMgUAUIQOAyAcdB8jaV75qqzr7Ae9Yaxk6JHFWoRq13zHOCBP6BFiIY03P6TRspg1E/avXbamoGAQiUJIAIlqRL3hCAwGoC04YJPy1ktDii4ZgW+kZVr8VUeFShGrXe3tgggR+SQgS9Paf/dBEyiAj2H05qCIHmBBDB5iGgAhCAwBIB5zENX6vqo9O8phcn+//vLZTjnmJ6yGdUoRq13kv9xP6OBF6lhAh6es44aabPNDtbde7oGkRwnJBSUwg0I4AINkNPwRCAgJdACMFE7itHetvsw9LaS5Jdd+2Q+YV1gYdsv1DVZ44yfk8yqlCNWu+l2CCB5wkhgks9Z7y/O2Y4IILjhZUaQ6A6AUSwOnIKhAAEYgmEEGzK5i+x90Wkj54WanmPKlSj1nsunkjgZTqIYMQnwSBJEcFBAkU1IdA5AUSw8wBRPQhA4DcCjpfZNaiSfj0fVahGrfelACOB813f8ewsrrE9VwIysuYjZ929sF/Hj7shAIHfCCCC9AQIQGAIAhGbJMS2J3pt4KGAUYVq1HpfkJEnjvWfh1s3e0TEXKdHBGM/EvpPjwj2HyNqCIERCCCCI0SJOkIAAu8JTFNEbf3f3FmAMbReqeqNmBuO044qVKPW+zROjAT6ei4i6OM0UipEcKRoUVcI9EsAEew3NtQMAhA4Q2A6TsJGga6vBPTcNpJZOn9wYaQlzP099lzCle1x374FEUQC3eH2TKtmaqgfZxcpEcEuwkAlIDA8AURw+BDSAAjsj0DEkRDn4NgB9A/PHTURS3JUoRq13of4IIFxPZURwTheI6RGBEeIEnWEQP8EEMH+Y0QNIQCBCwRCCLdE5IGI3HFAMgG04yFMAl870i8mGVWoRq23BQQJXOyWVxIggvHMer/D8RycPVe193ZRPwhAoC4BRLAub0qDAAQKEJhGCO3MQFvvd7rm74WdKxh7RqCnmqMK1cD1ZmMYT8c8SYMIJkDr/JZSMe282VQPAhDITAARzAyU7CAAgf0QGFiohlvbOK0N/cnZu3a5O+glNqWkgemJzt5YIFmpmBaoKllCAAIdE0AEOw4OVYMABPomgAjWi08IwaYAf+coEQlkRNDRTcZOggiOHT9qD4FeCCCCvUSCekAAAsMRQATrhczx4muVQQLPhMTBjl1D63XlLCWVimmWypEJBCAwDAFEcJhQUVEIQKA3AohgvYg4XnyRwAvhcLBDBOt15SwllYpplsqRCQQgMAwBRHCYUFFRCECgNwKIYL2I8OKbzroUO9YIpsdk7Z2lYrq2XtwPAQiMRQARHCte1BYCEOiIACJYLxi8+KazLsUOEUyPydo7S8V0bb24HwIQGIsAIjhWvKgtBCDQEQFEsF4wePFNZ12KHSKYHpO1d5aK6dp6cT8EIDAWAURwrHhRWwhAoCMCiGC9YPDim866FDtEMD0ma+8sFdO19eJ+CEBgLAKI4FjxorYQgEBHBBDBesHgxTeddSl2iGB6TNbeWSqma+vF/RCAwFgEEMGx4kVtIQCBjggggvWCwYtvOutS7BDB9JisvbNUTNfWi/shAIGxCCCCY8WL2kIAAh0RQATrBYMX33TWpdghgukxWXtnqZiurRf3QwACYxFABMeKF7WFAAQ6IoAI1gsGL77prEuxQwTTY7L2zlIxXVsv7ocABMYigAiOFS9qCwEIdEQAEawXDF5801mHEP5SRP58Jod/rap/GlsCIhhLLF96nod8LMkJAnsmgAjuOfq0HQIQWEUAEVyFL+pmXnyjcEkI4WMReSAi90XkU8fd70Tkkf1T1beO9FbGCxG5OZP2tqpaGq7MBHgeMgMlOwjslAAiuNPA02wIQGA9AURwPUNvDrz4ekmJCdotEXniFMDTjE0I76vqs6USEcElQuX+zvNQji05Q2BPBBDBPUWbtkIAAlkJIIJZcc5mxouvj3UIwUYBv/Olnk31WFUtr4sXIpiBcmIWPA+J4LgNAhD4gAAiSIeAAAQgkEgAEUwEl3AbL77L0ByMljP5MMVTVbWppWcvRDAWZ770jlh/q6oP85VIThCAwBYJIIJbjCptggAEqhBABKtgfl8IL77zrEMId0XkhwIR+VpVbe3glQsRLEDbmSXPgxMUySAAgVkCiCAdBAIQgEAiAUQwEVzCbbz4XoY2bQzzWkQ+SkDrueUzVbX8P7gQQQ+6Mml4HspwJVcI7I0AIri3iNNeCEAgGwFEMBvKxYx48Z0VQdsY5t4ixPQEZ6eIIoLpQNfcOYm/jdLOxZypoWsgcy8EdkIAEdxJoGkmBCCQnwAimJ/ppRwRwfNkJin41RmJpyLyX0Tk74nIfxaRvy8if+YcSbwyKogIOqlnSjbtBmtrNj3S/x9F5J9xfEcm+GQDgY0SQAQ3GliaBQEIlCeACJZnfCgBEbwogp5dQl9NR0L8fJpLCOGaiNhREdcXonllhAkRrNP/J9m3Ud87CSW+nGJ/ZWpvQl7cAgEIbIwAIrixgNIcCECgHgFEsCpr2wHxm5kSdzkVLoRgEjcnCHYu4I1za/yOJNsOnzdJnDt4/qWq2vmEv1+IYPn+H0K4ISIvnKO2lyrkPhuyfIsoAQIQ6IkAIthTNKgLBCAwFAFEsF64GBE8zzqEYCM9cwLnEuQQgk05/H4uoqr6wTsDIli2/2eSwONKfqmqNrLIBQEIQOA9AUSQjgABCEAgkQAimAgu4TZE8KIIhgWcZ3f8PL3Hs9YQEUzouIm3TFN2bZQ2906wn6vqlSnCidXkNghAYHACiODgAaT6EIBAOwKIYD32iGCaCJ7K21zEYvszI4Ll+r+DbWrhb1TV1oVyQQACEGBEkD4AAQhAIJVA7Itzajm57xux3ohgmgiKyCeq+tbTh2L7hUNWbrNrpYf8h2k803Tjc/3gDtd04ZVlcDsEIDAAAUYEBwgSVYQABPokEPvi3EsrRqw3InhRBJfWCLrWhXnkg6mhdZ7gEIJN3VzaxdUq80ZEjtf83XXe905VbYMgLghAYOcEEMGddwCaDwEIpBMYUaistSPWGxG8KIKeXUOvzY0KTusD2TU0/aMg253T2sBfHBl+rap2qPwH13TWoPWJpbWFX6iqpeOCAAR2TAAR3HHwaToEILCOwIhChQiui3lvd4cQvOcI3jongxHnCD5WVSvr94upofl7gzOes6O8026jPy3U7ko887eGHCEAgd4JIIK9R4j6QQAC3RJABOuFhhHB86wjRpDsLDmbRmijQDb6Z+fT2ZERNp1wafTICr+y+ygimL//hxAsRvdmcr5ynuO5tLnyyd9CcoQABHoigAj2FA3qAgEIDEUAEawXLkTwMmvHS//aQJ2VD0RwLdar9zuYetd83hKRH2dq+EpV7ccALghAYMcEEMEdB5+mQwAC6wggguv4xdyNCM6KoB0HUOLMuUOhZ88idEgLu4bGdPLf1u++EJGbM7e5mHpGimOOFolsBskhAIFBCCCCgwSKakIAAv0RQATrxQQRnGft2fUzMVpnNyWxvHJJS2K9NnmbgykjgpuMPI2CQBsCiGAb7pQKAQhsgAAiWC+IiOAy6wJTRJ+qqq0jPHs5pMU1erXcsv2kcMSQNYL76Q60FALFCSCCxRFTAAQgsFUCiGC9yCKCPtYOkfBlJLK4qyQi6EXpT8euoX5WpIQABNYTQATXMyQHCEBgpwQQwXqBRwT9rEMIthOo7T7p2Q30NGPbXfS+54w5RNAfE29Kz9q+KS/OEfRCJR0EIHCRACJI54AABCCQSAARTASXcBsiGAdtOiTezv2zfx4hNAG0A8ofzR0+f1wLRDAuJt7UIQTb+Oe6I/2bSfgPSe0HAM9971T1Y0f+JIEABDZOABHceIBpHgQgUI4AIliO7WnOiGAa60kI7SgB+2fHBdgOo5+KyCsReTvtNvrCMwJ4JiZZdrhMa9l27yq48c8B2req+nC7BGkZBCDgJYAIekmRDgIQgMAJAUSwXpdABOux9pbEiKCXVHw6B9v4TH+7442q2o8BXBCAAAQEEaQTQAACEEgkgAgmgku4DRFMgFb4FoessGtoYgymtYIlzob8XFUtXy4IQAACiCB9AAIQgEAqAUQwlVz8fYhgPLPSdyCCZQmHEGwqr02/9azx9FTGdQahJyPSQAAC2yDAiOA24kgrIACBBgQQwXrQEcF6rL0lIYJeUunpMsmgeyfY9JpyJwQgMCIBRHDEqFFnCECgOYEQgm2+8eNCRb4WkWeq+rp5hY8qMKLAIoI99aDf6oII1onJtOGPHQdyJ6HEl9NxIF19BiW0g1sgAIECBBDBAlDJEgIQ2C6B6Yw222bfdl70Xk9F5GEvQogIesNGujkCiGDd/jH9+HRfRO45Sn4+HQViU0u5IAABCJwlgAjSMSAAAQg4CKz8Vd5KsOlZJoMmkU0vRLAp/s0Ujgi2CeX0WWTrB21WwullG8HYcSB2NAgXBCAAgVkCiCAdBAIQgMACgWmdzrPIUcBLuT5VVftVv9mFCDZDv6mCEcFNhZPGQAACOySACO4w6DQZAhDwE5h+fbf1Nbl27rPCm8ogIuiPPykvE0AE6R0QgAAExiaACI4dP2oPAQgUJhBCsKlW1wsU02wrd0SwQDR3mCUiuMOg02QIQGBTBBDBTYWTxkAAAjkJhBAeiMh3OfM8ysvWDN5osYEMIlgoojvLFhHcWcBpLgQgsDkCiODmQkqDIACBHAQKTQk9rVqTKaKIYI4eQh6IIH0AAhCAwNgEEMGx40ftIQCBQgRCCLahy/eO7D/Ypn0SyLu2Q6hjcxkbFbxWe4c/RNARVZIsEkAEFxGRAAIQgEDXBBDBrsND5SAAgVYEHC+5VrWL6/wmIbQzvJbWF1ZfK4gItupV2yrX8YzcVlXOsdtW2GkNBCCwIQKI4IaCSVMgAIF8BJZkybPzp3N6afXpoUttU9XuvhtCCDbC+s1MhL9VVUvDVYkAIlgJNMVAAAIQKESguy/7Qu0kWwhAAAJuAtO5gT8t3PCZZ6MXh8C8VNVzB0O76xubEBGMJUb6cwQQQfoFBCAAgbEJIIJjx4/aQwACBQiEEEzMfpzJ+pWq3vAU7cgLEXSADCH8pYj8+UzS6iOrjmpvOgkiuOnw0jgIQGAHBBDBHQSZJkIAAnEEcspbzrziWnE59SgjgiEE23THNu0xMf/I2X7bvOeZqj5xpidZIgFEMBEct0EAAhDohAAi2EkgqAYEINAPAYe8vVPVjz01nmTmh5m03Y0IisgntXcyPeYz7djq2XV1LgS2I+sj+9eyLZ4+MmoaRHDUyFFvCEAAAr8RQATpCRCAAAROCEybvPy6AOYLVX22BC+EYGnuDCaCb2wkrvaOj9PaTBvJW9ppdQn78d+btCWmgqOmRQRHjRz1hgAEIIAI0gcgAAEIXCQQQni9cA6gCcaNudEm51mE1Xe7XJoaegTlqYg8qDGiFkJ4ICLfFeySj1XVyuDKRAARzASSbCAAAQg0IsCIYCPwFAsBCPRNIIRgI1P3Fmr5aho5+/k0XYTYfK6qV+4vSSdCBK0aNsXSRgcXRz9T6+xknZr98X3VxDZHZXvPAxHsPULUDwIQgMA8AUSQHgIBCEDgDAHnERKHO22DEpM5G0W8Nm1w8qkD7BtVtfRVL8d01XP1sTaaEL7NWdmKEvh7rFTVNqHhWkkAEVwJkNshAAEINCaACDYOAMVDAAL9EnC86K6t/JctdrecJPdFxE6ch3ZmHR10nLG4lu+l+zlqIgNZx/Nxu/Y60wzNIgsIQAACuyGACO4m1DQUAhCIJRA5KhibffXdQo8ruHJjltWjg46dWc/xtHWZJrB22UiqjcDaCKUdLxG7wYxrs5/YoO4pPSK4p2jTVghAYIsEEMEtRpU2QQAC2QhErPWLKdNG1myjGROZptc0KmebqHjP6TvUN3l0cNqV1abSeqbPWnm2ts+Ogbi4ljKEYGJoR04sres8rv+13FNdmwazcuGIYGXgFAcBCEAgMwFEMDNQsoMABLZHIPM6NhOoW7U3iJmLyiRRtjnOzYToRY8ORkwJjT76IXKkkymiCQE/3IIIroDHrRCAAAQ6IIAIdhAEqgABCPRPIEJe5hrTnQQeV3Ya/bRRtZTRwYeqage4z17TaKCNhC6VYTuymjBHb04zlWFiO3d+46Gen/UwMrvErce/I4I9RoU6QQACEPATQAT9rEgJAQjsnMC0rs0Ewzul8ZjYEEcXrBwdfDntLHpxyqvzbMVkCTwAn2TQ1hMurR2sfo7jVh4jRHArkaQdEIDAXgkggnuNPO2GAASSCUwyY+vqliTDyjABfDLa7omlRgcd8mDMspytOEmtrSucG31scoRHcufr6EZHLNk1tKN4URUIQAACpwQQQfoEBCAAgUQCk2jYjpW2UcnH038PG5q8GE3+TjGsHB28MtI2jdL9uoA767o955TeLOKZ2I2GvQ0RHDZ0VBwCEIDAewKIIB0BAhCAAARmCawYHfzkeI2f88iIrGv2nGsSm5znOHq3QwRHjyD1hwAE9k4AEdx7D6D9EIAABBwEptFB2wzGswHLIccPpgY6RudeqeoNR3Wikjh2fWWdYBTR3xIjggnQuAUCEIBARwQQwY6CQVUgAAEI9E4ghHDX1jw6dv20psSKYBEhc5wF+VJVbYovVwQBRDACFkkhAAEIdEgAEewwKFQJAhCAQM8EIo5nOBXBZwsjil97jqCIZeOYkooIxkJlRDCBGLdAAAIQ6IsAIthXPKgNBCAAgWEIxI4IOdJ/oaomi1kvRDArzt8zc8STXUPLoCdXCEAAAlkIIIJZMJIJBCAAgf0RiBWBEIKtMfxqhlSpqaE2nfWHmXIZEUzovrHxTyiCWyAAAQhAoCABRLAgXLKGAAQgsGUCsSLg2Cwm69ERB/atyt1y7K1tsfHfOg/aBwEIQGA0AojgaBGjvhCAAAQ6IRArAiGE+yLy/Uz1ixzuHkKwsx2v1x6J7CRMxaoRG/9iFSFjCEAAAhBIIoAIJmHjJghAAAIQiBWBEIIdDfHTArms6wRblLmXnhEzUWV5AAAPG0lEQVQb/71woZ0QgAAERiGACI4SKeoJAQhAoDMCKSIQQni7cPRE1rMEHXU0qh8cfN8Z5m6r42DLZjHdRo+KQQACEBBBBOkFEIAABCCQRCBFBByHu1tdsmwa45iKamU9V1XbTIYrkkBK/COLIDkEIAABCBQkgAgWhEvWEIAABLZMIEUEnFM1DduXqmoH1yddUzkvHAffryonqXIbuSkl/htpOs2AAAQgsAkCiOAmwkgjIAABCNQnkCoCjvsOjUmStBCCjfCZRH60QKXI5jT1I9GmREccmRraJjSUCgEIQMBFABF0YSIRBCAAAQicEkgVAccB78dFPReRB6r6eikCIYSPReThwlmFx9kkieZSPfby99T474UP7YQABCDQOwFEsPcIUT8IQAACnRJYIwKOw+VPW/1URJ6JyAtVtQ1nfr+mEUAbBbR/S6OAh/s4RH5lv1oT/5VFczsEIAABCGQggAhmgEgWEIAABPZIYI0ITKN3dr7fpwns3omI3WvHUXjF77gYu/+GZ5QxoW67uWVN/HcDiYZCAAIQ6JgAIthxcKgaBCAAgZ4JrBWBiA1dcmP4XFVNJLlWEFgb/xVFcysEIAABCGQggAhmgEgWEIAABPZIIIcINJBB1gVm6qw54p+pKmQDAQhAAAIJBBDBBGjcAgEIQAACIrlEoJIM2nTQu6pqR0pwrSQwxczWbM5N7f0fIvLXtq7T1ncyFXcldG6HAAQgkJkAIpgZKNlBAAIQ2AuBXCJovKY1gyYWNwvweyUi95kOup5sCOG+7eIqItcTcrMdYB8h4wnkuAUCEIBAAQKIYAGoZAkBCEBgDwRyiuCB1yQajxI3gTnFbqOAJh52pATXCgLTCKCdzZgigKcl2w6wdiTIB7u/rqget0IAAhCAQAIBRDABGrdAAAIQgEC+qaGnLKfRwcPIU8quom+mA+VNApGNlZ01hGAjgN+tzOacpN9ilDYzVbKDAAQgEEEAEYyARVIIQAACEPgDgRIjgmek0I6IsPMBby0cF/FyOlLC1qKxDjBTRw0h2CjgvUzZncuGzXsKwiVrCEAAAnMEEEH6BwQgAAEIJBGoIYLnKjZNU/xYRN4yopQUOtdNFSTwUI8vVNXWh3JBAAIQgEBFAohgRdgUBQEIQGBLBFqJ4JYY9tqWQtNBLzXX1nIyTbTXzkC9IACBzRJABDcbWhoGAQhAoCwBRLAs31a5TyOuPyWUb2szXy9M4b2U7StVtWnAXBCAAAQgUIkAIlgJNMVAAAIQ2BoBRHBrEf2tPY64HjfcdgC1dZkfTO0MIVyb1nXajq3eDX++VlXbMZYLAhCAAAQqEEAEK0CmCAhAAAJbJOAQhtts3DJW5EMItinPj45au89mnKaZmhB+tJCvTRG9xk6vDvokgQAEIJCBACKYASJZQAACENgjAURwe1EPIdjI3p2FlpkE2po+99Ec03RT2811SQbZRXR73YoWQQACnRJABDsNDNWCAAQg0DsBRLD3CMXVb5rO+cvCXbYO8EaMBB7yc442slYwLmykhgAEIJBMABFMRseNEIAABPZNABHcVvxDCPdF5PuFVq2a7htCsDWAXy2U8UmKaG4rGrQGAhCAQHkCiGB5xpQAAQhAYJMEEMFthdVxbuBLVbU1hMmXc9RxlWwmV44bIQABCOyMACK4s4DTXAhAAAK5CCCCuUj2kY8jnll29XSU862q2uYyXBCAAAQgUJAAIlgQLllDAAIQ2DIBxws9IzsDdYAQws8icn2mylniGUIwyftmphxEcKB+Q1UhAIFxCSCC48aOmkMAAhBoSgARbIo/e+EhhLCQaZa1e4hg9tCRIQQgAIEkAohgEjZuggAEIAABRHBbfcAhgp+p6uu1rUYE1xLkfghAAAJ5CCCCeTiSCwQgAIHdEUAEtxXyWvF0bErD1NBtdS1aAwEIdEoAEew0MFQLAhCAQO8EaolD7xy2Uj9HPB+r6oO17Q0h2KjipzP5cKj8WsjcDwEIQMBBABF0QCIJBCAAAQhcJeAQhyybi8C+DgHHlM13InJtzRl/zkPlP1dV27iGCwIQgAAEChJABAvCJWsIQAACWyaACG4ruk5JWzVt07Ez6TtV/XhbZGkNBCAAgT4JIIJ9xoVaQQACEOieACLYfYiiKxhCeCsiHy3cmDTS61gbaMU+VdX70RXnBghAAAIQiCaACEYj4wYIQAACEDACiOD2+kEI4ZGIfLXQMpsiel9Vn3kJOCXQskuSTG89SAcBCEAAAn8ggAjSGyAAAQhAwEVgmjp4Q0Ts37Xp8PG5aXy81LvI9pMohGBx/cVZo6ci8nDuSImpz5hczh1UfyjuparecpZNMghAAAIQWEkAEVwJkNshAAEIbJlACMGkz3aKvOuYMniK4l+JyL9cs7nIltn22jbHpjGnVX8uIi9E5LDBi/04YP3G+oxHAA/5sUlMr52CekEAApskgAhuMqw0CgIQgMA6AtNIzkMRubkuJ7FphI9U1fLiGoBACMFEzsQuRuLWtizL0RRrK8H9EIAABPZEABHcU7RpKwQgAIEFApMEmLQtrROLZflmWldmgsHVOYFpJNhitbRxTI6WvFJVG0HkggAEIACBigQQwYqwKQoCEIBAzwSm9WG2AUjJkaCvVdXWjHF1TqCSDL4SkVtMH+68M1A9CEBgkwQQwU2GlUZBAAIQiCNQ6aX/UCmOCIgLT7PUhfsFEtgsshQMAQhAQAQRpBdAAAIQ2DmBwi/7l+gig4P0u2mk+EmG9aLHLWZN4CDxp5oQgMB2CSCC240tLYMABCCwSKDRxiCHen2pqiYYXAMQCCHY7rG2fnTNusGX05ETrBUdIOZUEQIQ2DYBRHDb8aV1EIAABGYJhBBsTeCdCEy26YvdY0cFvD66709F5B+JyJ9E5GU7itr6sMOxAxG3krQFgemHAzsWwqQwZi2pHTFhu8cigC0CR5kQgAAEzhBABOkWEIAABHZKYDoi4kdn820914OlF/lJFEwSvnHmyyHiTlC9JZumjNoB8HYI/elB8PYjgf17sdRnemsX9YEABCCwFwKI4F4iTTshAAEInBAIIdiL+qcOMN/GngM4rTu0aZ+eUSOmiDqCQBIIQAACEIBATgKIYE6a5AUBCEBgEAIhhPsi8r2jusmSFrH+8I2q2qgSFwQgAAEIQAAClQgggpVAUwwEIACBngg41wauPvNvkkFbA7g08vg5awV76iHUBQIQgAAEtk4AEdx6hGkfBCAAgRMCk5z9ugAm29o951pEjhOgp0IAAhCAAAQqEkAEK8KmKAhAAAI9EHBOC/1MVY93BV1V9RCC7RZ5cyYTpoeuIszNEIAABCAAgTgCiGAcL1JDAAIQGJ5ACME2cbk305Bso4GHMpyjglnlc/hA0QAIQAACEIBAQQKIYEG4ZA0BCECgRwKO0bnVawPPtTuE8HbhMPLbHDXQY4+hThCAAAQgsEUCiOAWo0qbIAABCMwQaCVkrQSUzgABCEAAAhCAwFUCiCC9AgIQgMDOCIQQwlyTVbXId0MI4eHCQfPR5xXuLHQ0FwIQgAAEIJCNQJEv+2y1IyMIQAACEMhOABHMjpQMIQABCEAAAsMRQASHCxkVhgAEILCOACK4jh93QwACEIAABLZAABHcQhRpAwQgAIEIAh2vEfxSVW1HUy4IQAACEIAABAoTQAQLAyZ7CEAAAr0RaLVpSysB7Y0/9YEABCAAAQj0QAAR7CEK1AECEIBARQIhhEci8tVMka9U9UbOKoUQ7orIDwt5fqKqdsQEFwQgAAEIQAAChQkggoUBkz0EIACB3giEEO6LyPcL9cp6pp9jFDK7fPbGnfpAAAIQgAAEeiKACPYUDeoCAQhAoAKBEMLHIvLrQlEvVfVWjuo4RwMfq+qDHOWRBwQgAAEIQAACywQQwWVGpIAABCCwOQIhhGcicmehYavP9QshXBORn0Xko4WyPldVS8cFAQhAAAIQgEAFAohgBcgUAQEIQKA3AiEEG+370VGv5J08p5HHFyJyfaGcbKOPjvaQBAIQgAAEIAABEUEE6QYQgAAEdkoghGAjcEuSZnSiRwYn0bRRx6WRQMs/63rEnYaTZkMAAhCAAASiCCCCUbhIDAEIQGA7BCJGBa3Rr0Tkoaqa3F28Qgi226it9bvnJMVooBMUySAAAQhAAAI5CSCCOWmSFwQgAIHBCDiOkjht0RsRsemeNpp4WNNn8mdrAW26qWeE8ZDnOxG5oaqvB8NGdSEAAQhAAALDE0AEhw8hDYAABCCQTiBiHV96IZfv/GJphLFEoeQJAQhAAAIQgABrBOkDEIAABHZPYJrOaaN8nvV8uXglb0KTqwLkAwEIQAACENgzAUYE9xx92g4BCEBgIlBZBpFAeh4EIAABCECgMQFEsHEAKB4CEIBALwSmM/9sM5iYdX4x1bc1gQ9U9UnMTaSFAAQgAAEIQCA/AUQwP1NyhAAEIDAsgWnN4EMR+SpzI16KyH02hslMlewgAAEIQAACiQQQwURw3AYBCEBgywSmoyVMCG+ubKftMmrHTjAKuBIkt0MAAhCAAARyEkAEc9IkLwhAAAIbIzAJ4X0RuRuxmYxNAbUpps/YFXRjHYLmQAACEIDAZggggpsJJQ2BAAQgUJbAtKGMnRX4sZ3/N/3XCn07nSn4/r+qajuQckEAAhCAAAQg0DEBRLDj4FA1CEAAAhCAAAQgAAEIQAACJQgggiWokicEIAABCEAAAhCAAAQgAIGOCSCCHQeHqkEAAhCAAAQgAAEIQAACEChBABEsQZU8IQABCEAAAhCAAAQgAAEIdEwAEew4OFQNAhCAAAQgAAEIQAACEIBACQKIYAmq5AkBCEAAAhCAAAQgAAEIQKBjAohgx8GhahCAAAQgAAEIQAACEIAABEoQQARLUCVPCEAAAhCAAAQgAAEIQAACHRNABDsODlWDAAQgAAEIQAACEIAABCBQggAiWIIqeUIAAhCAAAQgAAEIQAACEOiYACLYcXCoGgQgAAEIQAACEIAABCAAgRIEEMESVMkTAhCAAAQgAAEIQAACEIBAxwQQwY6DQ9UgAAEIQAACEIAABCAAAQiUIIAIlqBKnhCAAAQgAAEIQAACEIAABDomgAh2HByqBgEIQAACEIAABCAAAQhAoAQBRLAEVfKEAAQgAAEIQAACEIAABCDQMYH/D9UfP1/1NPwHAAAAAElFTkSuQmCC", "e": 1}, {"id": "comp_0", "layers": [{"ddd": 1, "ind": 1, "ty": 5, "nm": "Call Back", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [145.448, 534.552, 0], "ix": 2}, "a": {"a": 0, "k": [0.676, 6.628, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "Call\rBack", "j": 2, "tr": 0, "lh": 56, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 2, "ty": 4, "nm": "CallBackBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [146.075, 536.342, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 3, "ty": 5, "nm": "Arrow Functions", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [356.523, 928.394, 0], "ix": 2}, "a": {"a": 0, "k": [0.676, 6.628, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "Arrow\rFunctions", "j": 2, "tr": 0, "lh": 56, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 4, "ty": 4, "nm": "ArrowFunctionBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [356.523, 930.806, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 5, "ty": 5, "nm": "AJAX", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [724.27, 929.193, 0], "ix": 2}, "a": {"a": 0, "k": [69.019, -17.422, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "AJAX", "j": 0, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 6, "ty": 4, "nm": "AjaxBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [724.27, 929.193, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 7, "ty": 5, "nm": "DOM", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [934.416, 537.749, 0], "ix": 2}, "a": {"a": 0, "k": [62.184, -17.649, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "DOM", "j": 0, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 8, "ty": 4, "nm": "DomBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [933, 536.5, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 9, "ty": 5, "nm": "Events", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [724.668, 142.916, 0], "ix": 2}, "a": {"a": 0, "k": [86.225, -17.372, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "Events", "j": 0, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 10, "ty": 4, "nm": "EventsBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [724.668, 142.916, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 11, "ty": 5, "nm": "Prototype", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [358, 143.22, 0], "ix": 2}, "a": {"a": 0, "k": [127.113, -10.927, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "Prototype", "j": 0, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 12, "ty": 4, "nm": "PrototypeBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [356, 142.5, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 13, "ty": 0, "nm": "circuit", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [540, 535, 0], "ix": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1}, "s": {"a": 0, "k": [21.069, 21.069, 21.069], "ix": 6}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 1410.00005743048, "s": [47.047]}], "ix": 2}, "w": 1080, "h": 1080, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 14, "ty": 5, "nm": "JS", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [537.705, 531.851, 0], "ix": 2}, "a": {"a": 0, "k": [-0.295, -17.649, 0], "ix": 1}, "s": {"a": 0, "k": [137.27, 137.27, 137.27], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Glow", "np": 16, "mn": "ADBE Glo2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Glow Based On", "mn": "ADBE Glo2-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "Glow Threshold", "mn": "ADBE Glo2-0002", "ix": 2, "v": {"a": 0, "k": 153, "ix": 2}}, {"ty": 0, "nm": "<PERSON><PERSON> Radius", "mn": "ADBE Glo2-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [10]}, {"t": 11.0000004480392, "s": [6]}], "ix": 3}}, {"ty": 0, "nm": "Glow Intensity", "mn": "ADBE Glo2-0004", "ix": 4, "v": {"a": 0, "k": 1, "ix": 4}}, {"ty": 7, "nm": "Composite Original", "mn": "ADBE Glo2-0005", "ix": 5, "v": {"a": 0, "k": 2, "ix": 5}}, {"ty": 7, "nm": "Glow Operation", "mn": "ADBE Glo2-0006", "ix": 6, "v": {"a": 0, "k": 3, "ix": 6}}, {"ty": 7, "nm": "Glow Colors", "mn": "ADBE Glo2-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Color Looping", "mn": "ADBE Glo2-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Color Loops", "mn": "ADBE Glo2-0009", "ix": 9, "v": {"a": 0, "k": 1, "ix": 9}}, {"ty": 0, "nm": "Color Phase", "mn": "ADBE Glo2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10}}, {"ty": 0, "nm": "A & B Midpoint", "mn": "ADBE Glo2-0011", "ix": 11, "v": {"a": 0, "k": 0.5, "ix": 11}}, {"ty": 2, "nm": "Color A", "mn": "ADBE Glo2-0012", "ix": 12, "v": {"a": 0, "k": [1, 1, 1, 0], "ix": 12}}, {"ty": 2, "nm": "Color B", "mn": "ADBE Glo2-0013", "ix": 13, "v": {"a": 0, "k": [0, 0, 0, 0], "ix": 13}}, {"ty": 7, "nm": "Glow Dimensions", "mn": "ADBE Glo2-0014", "ix": 14, "v": {"a": 0, "k": 1, "ix": 14}}]}], "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-Bold", "t": "JS", "j": 2, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 15, "ty": 4, "nm": "JsBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [538.075, 536.342, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [119.636, 119.636, 119.636], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-182, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 16, "ty": 4, "nm": "Strokes", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[66, 119], [66, 234], [181, 234], [181, 285]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 6", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-71, 119], [-71, 234], [-185, 234], [-185, 287]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 2", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 5", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[122, 0], [286, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 2", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 4", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-123, 0], [-290, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 2", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 3", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[67, -128], [67, -243], [181, -243], [181, -292]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 2", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-70, -128], [-70, -242], [-186, -242], [-186, -295]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 24, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 2, "ix": 7, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "wave", "td": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.361], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.361], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [100]}, {"i": {"x": [0.361], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [100]}, {"t": 30.0000012219251, "s": [0]}], "ix": 11, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2}, "a": {"a": 0, "k": [400.892, 398.702, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.565, 0.565, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [48.721, 48.721, 100]}, {"i": {"x": [0, 0, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 26, "s": [120.62, 120.62, 100]}, {"t": 30.0000012219251, "s": [149.721, 149.721, 100]}], "ix": 6, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 80.8, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [830.264, 830.264], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 150, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [400.892, 398.702], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 121.000004928431, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Layer 1", "tt": 3, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [537.296, 531.457, 0], "ix": 2}, "a": {"a": 0, "k": [448.977, 448.977, 0], "ix": 1}, "s": {"a": 0, "k": [120.489, 120.489, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 54.2, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "ip": 0, "op": 121.000004928431, "st": 0, "bm": 2}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "wave 2", "td": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.361], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.361], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [100]}, {"i": {"x": [0.361], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [100]}, {"t": 30.0000012219251, "s": [0]}], "ix": 11, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2}, "a": {"a": 0, "k": [400.892, 398.702, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.65, 0.65, 0.667], "y": [0.697, 0.697, -19.249]}, "o": {"x": [0.359, 0.359, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [28.232, 28.232, 100]}, {"i": {"x": [0.611, 0.611, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.29, 0.29, 0.333], "y": [4.913, 4.913, 14.849]}, "t": 15, "s": [97.526, 97.526, 100]}, {"i": {"x": [0, 0, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 26, "s": [100.131, 100.131, 100]}, {"t": 30.0000012219251, "s": [129.232, 129.232, 100]}], "ix": 6, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 80.8, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [830.264, 830.264], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 150, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [400.892, 398.702], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 121.000004928431, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "Layer 2", "tt": 3, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [537.296, 531.457, 0], "ix": 2}, "a": {"a": 0, "k": [448.977, 448.977, 0], "ix": 1}, "s": {"a": 0, "k": [120.489, 120.489, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 121.000004928431, "st": 0, "bm": 2}]}, {"id": "comp_2", "layers": [{"ddd": 1, "ind": 1, "ty": 5, "nm": "Call Back", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [157.448, 542.552, 0], "ix": 2}, "a": {"a": 0, "k": [0.676, 6.628, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "Call\rBack", "j": 2, "tr": 0, "lh": 56, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 2, "ty": 4, "nm": "CallBackBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [158.075, 544.342, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 23, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 3, "ty": 5, "nm": "Arrow Functions", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [368.523, 936.394, 0], "ix": 2}, "a": {"a": 0, "k": [0.676, 6.628, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "Arrow\rFunctions", "j": 2, "tr": 0, "lh": 56, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 4, "ty": 4, "nm": "ArrowFunctionBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [368.523, 938.806, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 23, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 5, "ty": 5, "nm": "AJAX", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [736.27, 937.193, 0], "ix": 2}, "a": {"a": 0, "k": [69.019, -17.422, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "AJAX", "j": 0, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 6, "ty": 4, "nm": "AjaxBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [736.27, 937.193, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 23, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 7, "ty": 5, "nm": "DOM", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [946.416, 545.749, 0], "ix": 2}, "a": {"a": 0, "k": [62.184, -17.649, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "DOM", "j": 0, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 8, "ty": 4, "nm": "DomBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [945, 544.5, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 23, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 9, "ty": 5, "nm": "Events", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [736.668, 150.916, 0], "ix": 2}, "a": {"a": 0, "k": [86.225, -17.372, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "Events", "j": 0, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 10, "ty": 4, "nm": "EventsBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [736.668, 150.916, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 23, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 11, "ty": 5, "nm": "Prototype", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [370, 151.22, 0], "ix": 2}, "a": {"a": 0, "k": [127.113, -10.927, 0], "ix": 1}, "s": {"a": 0, "k": [60.32, 60.32, 60.32], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-SemiBold", "t": "Prototype", "j": 0, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 12, "ty": 4, "nm": "PrototypeBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [368, 150.5, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [99.057, 99.057, 99.057], "ix": 6}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 23, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-184, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 13, "ty": 0, "nm": "circuit", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [552, 543, 0], "ix": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1}, "s": {"a": 0, "k": [21.069, 21.069, 21.069], "ix": 6}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 1410.00005743048, "s": [47.047]}], "ix": 2}, "w": 1080, "h": 1080, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 14, "ty": 5, "nm": "JS", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [549.705, 539.851, 0], "ix": 2}, "a": {"a": 0, "k": [-0.295, -17.649, 0], "ix": 1}, "s": {"a": 0, "k": [137.27, 137.27, 137.27], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Glow", "np": 16, "mn": "ADBE Glo2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Glow Based On", "mn": "ADBE Glo2-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "Glow Threshold", "mn": "ADBE Glo2-0002", "ix": 2, "v": {"a": 0, "k": 153, "ix": 2}}, {"ty": 0, "nm": "<PERSON><PERSON> Radius", "mn": "ADBE Glo2-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [10]}, {"t": 11.0000004480392, "s": [6]}], "ix": 3}}, {"ty": 0, "nm": "Glow Intensity", "mn": "ADBE Glo2-0004", "ix": 4, "v": {"a": 0, "k": 1, "ix": 4}}, {"ty": 7, "nm": "Composite Original", "mn": "ADBE Glo2-0005", "ix": 5, "v": {"a": 0, "k": 2, "ix": 5}}, {"ty": 7, "nm": "Glow Operation", "mn": "ADBE Glo2-0006", "ix": 6, "v": {"a": 0, "k": 3, "ix": 6}}, {"ty": 7, "nm": "Glow Colors", "mn": "ADBE Glo2-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Color Looping", "mn": "ADBE Glo2-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Color Loops", "mn": "ADBE Glo2-0009", "ix": 9, "v": {"a": 0, "k": 1, "ix": 9}}, {"ty": 0, "nm": "Color Phase", "mn": "ADBE Glo2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10}}, {"ty": 0, "nm": "A & B Midpoint", "mn": "ADBE Glo2-0011", "ix": 11, "v": {"a": 0, "k": 0.5, "ix": 11}}, {"ty": 2, "nm": "Color A", "mn": "ADBE Glo2-0012", "ix": 12, "v": {"a": 0, "k": [1, 1, 1, 0], "ix": 12}}, {"ty": 2, "nm": "Color B", "mn": "ADBE Glo2-0013", "ix": 13, "v": {"a": 0, "k": [0, 0, 0, 0], "ix": 13}}, {"ty": 7, "nm": "Glow Dimensions", "mn": "ADBE Glo2-0014", "ix": 14, "v": {"a": 0, "k": 1, "ix": 14}}]}], "t": {"d": {"k": [{"s": {"s": 50, "f": "Poppins-Bold", "t": "JS", "j": 2, "tr": 0, "lh": 60, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 15, "ty": 4, "nm": "JsBg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [550.075, 544.342, 0], "ix": 2}, "a": {"a": 0, "k": [-184, -397.5, 0], "ix": 1}, "s": {"a": 0, "k": [119.636, 119.636, 119.636], "ix": 6}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 23, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [212, 213], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.305882352941, 0.811764765721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-182, -397.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 16, "ty": 4, "nm": "Strokes", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [552, 548, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[66, 119], [66, 234], [181, 234], [181, 285]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 6", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-71, 119], [-71, 234], [-185, 234], [-185, 287]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 2", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 5", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[122, 0], [286, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 2", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 4", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-123, 0], [-290, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 2", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 3", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[67, -128], [67, -243], [181, -243], [181, -292]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 2", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-70, -128], [-70, -242], [-186, -242], [-186, -295]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.243137254902, 0.360784313725, 0.905882352941, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 24, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 31, "ix": 1}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 30.0000012219251, "s": [-14]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 2, "ix": 7, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}]}], "fonts": {"list": [{"fName": "Poppins-Bold", "fFamily": "<PERSON><PERSON><PERSON>", "fStyle": "Bold", "ascent": 73.9990234375}, {"fName": "Poppins-SemiBold", "fFamily": "<PERSON><PERSON><PERSON>", "fStyle": "SemiBold", "ascent": 73.9990234375}]}, "layers": [{"ddd": 1, "ind": 1, "ty": 0, "nm": "OrientationJs", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": -1, "ix": 9}, "rz": {"a": 0, "k": 44, "ix": 10}, "or": {"a": 0, "k": [311, 0, 0], "ix": 7}, "p": {"a": 0, "k": [1002.012, 594.94, 0], "ix": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1}, "s": {"a": 0, "k": [130.497, 130.497, 130.497], "ix": 6}}, "ao": 0, "w": 1080, "h": 1080, "ip": 0, "op": 119.000004846969, "st": 0, "bm": 0}, {"ddd": 1, "ind": 2, "ty": 0, "nm": "shadow", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 60, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": -1, "ix": 9}, "rz": {"a": 0, "k": 44, "ix": 10}, "or": {"a": 0, "k": [311, 0, 0], "ix": 7}, "p": {"a": 0, "k": [1002.012, 594.94, 0], "ix": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1}, "s": {"a": 0, "k": [130.497, 130.497, 130.497], "ix": 6}}, "ao": 0, "w": 1080, "h": 1080, "ip": 0, "op": 1410.00005743048, "st": 0, "bm": 0}], "markers": [], "chars": [{"ch": "J", "size": 50, "style": "Bold", "w": 57.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.129, 0], [0, 6.042], [0, 0], [-14.502, 0], [0, 15.207], [0, 0], [0, 0]], "o": [[0, 5.237], [-4.431, 0], [0, 0], [0, 15.912], [13.998, 0], [0, 0], [0, 0], [0, 0]], "v": [[33.334, -22.559], [26.788, -14.703], [19.739, -23.666], [2.618, -23.666], [27.292, 0.705], [50.555, -22.559], [50.555, -70.697], [33.334, -70.697]], "c": true}, "ix": 2}, "nm": "J", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "J", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "S", "size": 50, "style": "Bold", "w": 61.5, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 11.884], [0, 12.286], [-4.129, -0.101], [-0.302, -4.532], [0, 0], [15.408, 0], [0, -13.394], [0, -13.092], [5.035, 0], [0.504, 5.438], [0, 0], [-15.207, 0]], "o": [[0, -23.666], [0, -4.431], [4.633, 0.101], [0, 0], [-0.705, -13.495], [-14.804, 0], [-0.201, 25.076], [0, 4.129], [-4.834, 0], [0, 0], [0.504, 14.301], [16.516, 0]], "v": [[57.605, -21.048], [22.659, -51.462], [29.91, -57.907], [38.068, -50.555], [56.699, -50.555], [30.212, -71.704], [4.431, -50.858], [39.578, -19.739], [31.32, -13.092], [22.559, -21.149], [4.23, -21.149], [31.824, 0.705]], "c": true}, "ix": 2}, "nm": "S", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "S", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "P", "size": 50, "style": "SemiBold", "w": 60.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, -6.445], [7.956, 0]], "o": [[0, 0], [0, 0], [7.956, 0], [0, 6.244], [0, 0]], "v": [[21.048, -38.37], [21.048, -58.813], [32.227, -58.813], [43.707, -48.541], [32.227, -38.37]], "c": true}, "ix": 2}, "nm": "P", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 10.675], [16.919, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, -12.186], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [18.127, 0]], "v": [[58.209, -48.541], [32.831, -70.294], [6.949, -70.294], [6.949, 0], [21.048, 0], [21.048, -26.99], [32.831, -26.99]], "c": true}, "ix": 2}, "nm": "P", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "P", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "r", "size": 50, "style": "SemiBold", "w": 40.4, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-8.359, 0], [0, 0], [0, 0], [3.525, -5.74], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, -10.776], [0, 0], [0, 0], [-7.755, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[21.048, -27.795], [34.442, -41.794], [38.168, -41.794], [38.168, -56.598], [21.048, -47.131], [21.048, -55.792], [6.949, -55.792], [6.949, 0], [21.048, 0]], "c": true}, "ix": 2}, "nm": "r", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "r", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "o", "size": 50, "style": "SemiBold", "w": 63.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 17.523], [16.113, 0], [0, -17.624], [-16.113, 0]], "o": [[0, -17.624], [-16.113, 0], [0, 17.523], [16.214, 0]], "v": [[60.828, -27.896], [32.126, -56.699], [3.424, -27.896], [31.723, 0.906]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 11.078], [-7.452, 0], [0, -11.179], [7.452, 0]], "o": [[0, -11.179], [7.452, 0], [0, 11.078], [-7.553, 0]], "v": [[17.725, -27.896], [31.924, -44.412], [46.326, -27.896], [31.723, -11.38]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "o", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "t", "size": 50, "style": "SemiBold", "w": 38.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-10.675, 0], [0, 0], [0, 0], [0, 0], [0, 3.726], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 12.79], [0, 0], [0, 0], [0, 0], [-4.431, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.164, -17.322], [26.99, 0], [35.852, 0], [35.852, -11.884], [29.306, -11.884], [23.364, -17.221], [23.364, -44.211], [35.852, -44.211], [35.852, -55.792], [23.364, -55.792], [23.364, -69.589], [9.164, -69.589], [9.164, -55.792], [2.518, -55.792], [2.518, -44.211], [9.164, -44.211]], "c": true}, "ix": 2}, "nm": "t", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "t", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "y", "size": 50, "style": "SemiBold", "w": 60.5, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.716, -16.818], [16.315, -55.792], [0.504, -55.792], [22.861, -1.41], [10.776, 26.385], [25.781, 26.385], [60.324, -55.792], [45.319, -55.792]], "c": true}, "ix": 2}, "nm": "y", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "y", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "p", "size": 50, "style": "SemiBold", "w": 67.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.323, -4.733], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-8.762, 0], [0, 17.422], [14.301, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [3.424, 4.532], [14.301, 0], [0, -17.422], [-8.661, 0]], "v": [[21.048, -47.736], [21.048, -55.792], [6.949, -55.792], [6.949, 26.587], [21.048, 26.587], [21.048, -7.956], [39.478, 0.906], [64.957, -28.098], [39.478, -56.699]], "c": true}, "ix": 2}, "nm": "p", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, -10.574], [7.654, 0], [0, 10.574], [-7.553, 0]], "o": [[0, 10.574], [-7.553, 0], [0, -10.574], [7.654, 0]], "v": [[50.555, -28.098], [35.751, -11.481], [21.048, -27.896], [35.751, -44.312]], "c": true}, "ix": 2}, "nm": "p", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "p", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "e", "size": 50, "style": "SemiBold", "w": 61.7, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.747, 0], [-0.201, -7.352], [0, 0]], "o": [[7.251, 0], [0, 0], [1.108, -7.654]], "v": [[30.917, -45.117], [44.211, -33.133], [17.725, -33.133]], "c": true}, "ix": 2}, "nm": "e", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-3.021, 10.272], [0, 0], [6.042, 0], [0.705, 8.258], [0, 0], [0, 1.813], [16.113, 0], [0, -17.624], [-16.214, 0]], "o": [[0, 0], [-1.813, 3.726], [-7.05, 0], [0, 0], [0.302, -1.813], [0, -16.617], [-16.516, 0], [0, 17.523], [13.797, 0]], "v": [[57.404, -17.523], [42.197, -17.523], [31.018, -10.776], [17.624, -23.666], [58.411, -23.666], [58.813, -29.105], [31.32, -56.699], [3.323, -27.896], [31.32, 0.906]], "c": true}, "ix": 2}, "nm": "e", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "e", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "E", "size": 50, "style": "SemiBold", "w": 53.2, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[47.736, -70.395], [6.949, -70.395], [6.949, 0], [47.736, 0], [47.736, -11.481], [21.048, -11.481], [21.048, -30.112], [44.714, -30.112], [44.714, -41.29], [21.048, -41.29], [21.048, -58.914], [47.736, -58.914]], "c": true}, "ix": 2}, "nm": "E", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "E", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "v", "size": 50, "style": "SemiBold", "w": 59.9, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.007, -55.792], [21.552, 0], [38.672, 0], [59.317, -55.792], [44.312, -55.792], [30.212, -12.991], [16.113, -55.792]], "c": true}, "ix": 2}, "nm": "v", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "v", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "n", "size": 50, "style": "SemiBold", "w": 66.1, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [12.991, 0], [3.625, -4.733], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.654, 0], [0, -8.963]], "o": [[0, 0], [0, 0], [0, -15.408], [-7.15, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -8.963], [7.452, 0], [0, 0]], "v": [[46.024, 0], [60.123, 0], [60.123, -32.73], [37.866, -56.598], [21.048, -48.843], [21.048, -55.792], [6.949, -55.792], [6.949, 0], [21.048, 0], [21.048, -30.817], [33.636, -44.614], [46.024, -30.817]], "c": true}, "ix": 2}, "nm": "n", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "n", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "s", "size": 50, "style": "SemiBold", "w": 54.5, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 9.567], [0, 10.172], [-5.237, 0], [-0.403, -4.33], [0, 0], [13.495, 0], [0, -9.265], [0, -9.97], [5.539, 0], [0.504, 4.23], [0, 0], [-13.998, 0]], "o": [[-0.403, -20.444], [0, -3.223], [5.539, 0], [0, 0], [-0.806, -11.078], [-13.797, 0], [0, 20.444], [0, 3.223], [-5.64, 0], [0, 0], [0.604, 10.373], [13.596, 0]], "v": [[50.253, -15.912], [18.933, -40.182], [26.889, -45.52], [36.255, -38.269], [49.75, -38.269], [27.292, -56.699], [5.237, -39.981], [36.758, -15.912], [28.198, -10.172], [18.127, -17.624], [3.928, -17.624], [28.299, 0.906]], "c": true}, "ix": 2}, "nm": "s", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "s", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "D", "size": 50, "style": "SemiBold", "w": 71.7, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[22.458, 0], [0, 0], [0, 0], [0, 0], [0, 21.249]], "o": [[0, 0], [0, 0], [0, 0], [22.458, 0], [0, -21.35]], "v": [[31.522, -70.294], [6.949, -70.294], [6.949, 0], [31.522, 0], [68.582, -35.046]], "c": true}, "ix": 2}, "nm": "D", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, -14.603], [14.804, 0]], "o": [[0, 0], [0, 0], [14.804, 0], [0, 14.603], [0, 0]], "v": [[21.048, -11.984], [21.048, -58.411], [31.018, -58.411], [54.181, -35.046], [31.018, -11.984]], "c": true}, "ix": 2}, "nm": "D", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "D", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "O", "size": 50, "style": "SemiBold", "w": 78.5, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 21.149], [20.041, 0], [0, -21.048], [-19.839, 0]], "o": [[0, -21.048], [-19.839, 0], [0, 21.149], [19.94, 0]], "v": [[75.531, -35.349], [39.578, -71.301], [3.525, -35.349], [39.578, 0.705]], "c": true}, "ix": 2}, "nm": "O", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 14.301], [-12.79, 0], [0, -14.301], [12.689, 0]], "o": [[0, -14.301], [12.689, 0], [0, 14.301], [-12.79, 0]], "v": [[18.027, -35.349], [39.578, -58.612], [61.029, -35.349], [39.578, -11.884]], "c": true}, "ix": 2}, "nm": "O", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "O", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "M", "size": 50, "style": "SemiBold", "w": 89.9, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.949, 0], [21.048, 0], [21.048, -45.721], [39.981, 0], [50.656, 0], [69.489, -45.721], [69.489, 0], [83.588, 0], [83.588, -70.294], [67.676, -70.294], [45.319, -18.027], [22.961, -70.294], [6.949, -70.294]], "c": true}, "ix": 2}, "nm": "M", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "M", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "A", "size": 50, "style": "SemiBold", "w": 71.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[54.684, 0], [69.589, 0], [44.312, -70.395], [27.896, -70.395], [2.618, 0], [17.422, 0], [22.055, -13.394], [50.052, -13.394]], "c": true}, "ix": 2}, "nm": "A", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[46.225, -24.673], [25.882, -24.673], [36.053, -54.08]], "c": true}, "ix": 2}, "nm": "A", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "A", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "J", "size": 50, "style": "SemiBold", "w": 57, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [5.237, 0], [0.101, 5.942], [0, 0], [-13.193, 0], [0, 13.898], [0, 0], [0, 0]], "o": [[0, 5.539], [-5.338, 0], [0, 0], [0, 14.2], [12.991, 0], [0, 0], [0, 0], [0, 0]], "v": [[33.737, -20.947], [25.882, -11.884], [17.725, -21.451], [3.625, -21.451], [26.083, 0.705], [47.937, -20.947], [47.937, -70.294], [33.737, -70.294]], "c": true}, "ix": 2}, "nm": "J", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "J", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "X", "size": 50, "style": "SemiBold", "w": 68.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[49.045, 0], [65.259, 0], [42.801, -34.946], [64.856, -70.294], [48.944, -70.294], [35.248, -46.729], [20.041, -70.294], [3.827, -70.294], [26.285, -35.449], [4.129, 0], [20.041, 0], [33.838, -23.666]], "c": true}, "ix": 2}, "nm": "X", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "X", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "w", "size": 50, "style": "SemiBold", "w": 84.4, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[17.12, 0], [32.428, 0], [42.599, -38.974], [52.771, 0], [67.978, 0], [84.293, -55.792], [70.798, -55.792], [60.928, -13.394], [50.455, -55.792], [35.55, -55.792], [24.875, -13.293], [15.005, -55.792], [0.705, -55.792]], "c": true}, "ix": 2}, "nm": "w", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "w", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "\r", "size": 50, "style": "SemiBold", "w": 0, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "F", "size": 50, "style": "SemiBold", "w": 53, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.949, 0], [21.048, 0], [21.048, -29.709], [43.506, -29.709], [43.506, -40.887], [21.048, -40.887], [21.048, -58.914], [50.354, -58.914], [50.354, -70.294], [6.949, -70.294]], "c": true}, "ix": 2}, "nm": "F", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "F", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "u", "size": 50, "style": "SemiBold", "w": 66.1, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [7.654, 0], [0, 8.963], [0, 0], [0, 0], [0, 0], [-12.991, 0], [-3.525, 4.733], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 8.963], [-7.452, 0], [0, 0], [0, 0], [0, 0], [0, 15.308], [6.949, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.619, -55.792], [45.419, -55.792], [45.419, -25.076], [32.932, -11.279], [20.544, -25.076], [20.544, -55.792], [6.445, -55.792], [6.445, -23.062], [28.802, 0.705], [45.419, -7.05], [45.419, 0], [59.619, 0]], "c": true}, "ix": 2}, "nm": "u", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "u", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "c", "size": 50, "style": "SemiBold", "w": 60.2, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -17.523], [-16.113, 0], [-3.021, 11.884], [0, 0], [5.841, 0], [0, 10.876], [-7.855, 0], [-1.611, -4.733], [0, 0], [13.898, 0]], "o": [[0, 17.523], [13.898, 0], [0, 0], [-1.712, 5.035], [-7.855, 0], [0, -10.876], [5.841, 0], [0, 0], [-3.021, -12.488], [-16.113, 0]], "v": [[3.323, -27.896], [30.917, 0.906], [57.202, -18.832], [41.995, -18.832], [30.817, -11.179], [17.725, -27.896], [30.817, -44.714], [41.995, -36.96], [57.202, -36.96], [30.917, -56.699]], "c": true}, "ix": 2}, "nm": "c", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "c", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "i", "size": 50, "style": "SemiBold", "w": 27.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.949, 0], [21.048, 0], [21.048, -55.792], [6.949, -55.792]], "c": true}, "ix": 2}, "nm": "i", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-5.035, 0], [0, 4.633], [4.935, 0], [0, -4.633]], "o": [[4.935, 0], [0, -4.633], [-5.035, 0], [0, 4.633]], "v": [[14.099, -62.439], [22.76, -70.697], [14.099, -78.955], [5.438, -70.697]], "c": true}, "ix": 2}, "nm": "i", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "i", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "C", "size": 50, "style": "SemiBold", "w": 76.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -21.048], [-20.242, 0], [-4.733, 14.502], [0, 0], [7.553, 0], [0, 14.2], [-12.286, 0], [-3.323, -6.747], [0, 0], [15.61, 0]], "o": [[0, 21.048], [15.61, 0], [0, 0], [-3.323, 6.848], [-12.286, 0], [0, -14.301], [7.553, 0], [0, 0], [-4.733, -14.401], [-20.242, 0]], "v": [[3.525, -35.248], [39.175, 0.604], [72.208, -22.156], [55.994, -22.156], [39.075, -11.984], [18.027, -35.248], [39.075, -58.511], [55.994, -48.441], [72.208, -48.441], [39.175, -71.201]], "c": true}, "ix": 2}, "nm": "C", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "C", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "a", "size": 50, "style": "SemiBold", "w": 67.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -17.422], [-14.099, 0], [-3.323, 4.834], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.963, 0]], "o": [[0, 17.422], [8.862, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.323, -4.633], [-14.2, 0]], "v": [[3.323, -28.098], [28.702, 0.906], [47.232, -8.157], [47.232, 0], [61.432, 0], [61.432, -55.792], [47.232, -55.792], [47.232, -47.836], [28.802, -56.699]], "c": true}, "ix": 2}, "nm": "a", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, -10.574], [7.755, 0], [0, 10.574], [-7.553, 0]], "o": [[0, 10.574], [-7.553, 0], [0, -10.574], [7.755, 0]], "v": [[47.232, -27.896], [32.428, -11.481], [17.725, -28.098], [32.428, -44.312]], "c": true}, "ix": 2}, "nm": "a", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "a", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "l", "size": 50, "style": "SemiBold", "w": 27.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.949, 0], [21.048, 0], [21.048, -74.524], [6.949, -74.524]], "c": true}, "ix": 2}, "nm": "l", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "l", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "B", "size": 50, "style": "SemiBold", "w": 64.3, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -6.042], [6.848, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 5.942], [0, 0], [0, 0], [0, 0], [6.848, 0]], "v": [[46.124, -20.746], [35.248, -11.481], [21.048, -11.481], [21.048, -30.414], [34.946, -30.414]], "c": true}, "ix": 2}, "nm": "B", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, -5.64], [6.647, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 5.74], [0, 0], [0, 0], [0, 0], [6.647, 0]], "v": [[44.412, -50.052], [33.939, -41.29], [21.048, -41.29], [21.048, -58.914], [33.939, -58.914]], "c": true}, "ix": 2}, "nm": "B", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 11.179], [7.956, 1.41], [0, 8.56], [14.703, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, -8.258], [6.848, -2.216], [0, -10.474], [0, 0], [0, 0], [0, 0], [14.603, 0]], "v": [[60.425, -19.034], [46.829, -36.053], [58.813, -52.167], [35.953, -70.294], [6.949, -70.294], [6.949, 0], [37.262, 0]], "c": true}, "ix": 2}, "nm": "B", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "B", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}, {"ch": "k", "size": 50, "style": "SemiBold", "w": 58.4, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.949, 0], [21.048, 0], [21.048, -23.767], [39.981, 0], [58.31, 0], [33.536, -27.795], [58.109, -55.792], [39.78, -55.792], [21.048, -32.126], [21.048, -74.524], [6.949, -74.524]], "c": true}, "ix": 2}, "nm": "k", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "k", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "<PERSON><PERSON><PERSON>"}]}