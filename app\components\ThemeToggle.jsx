'use client';

import { useTheme } from '@/app/contexts/ThemeContext';
import { BsSun, BsMoon } from 'react-icons/bs';

const ThemeToggle = ({ className = '' }) => {
  const { theme, toggleTheme, isDark } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`
        relative inline-flex items-center justify-center
        w-12 h-12 rounded-full
        bg-gray-200 dark:bg-gray-700
        hover:bg-gray-300 dark:hover:bg-gray-600
        transition-all duration-300 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-offset-2 
        focus:ring-blue-500 dark:focus:ring-blue-400
        focus:ring-offset-white dark:focus:ring-offset-gray-800
        group
        ${className}
      `}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      title={`Switch to ${isDark ? 'light' : 'dark'} mode`}
    >
      {/* Sun Icon */}
      <BsSun 
        className={`
          absolute w-5 h-5 text-yellow-500
          transition-all duration-300 ease-in-out
          ${isDark 
            ? 'opacity-0 rotate-90 scale-0' 
            : 'opacity-100 rotate-0 scale-100'
          }
        `}
      />
      
      {/* Moon Icon */}
      <BsMoon 
        className={`
          absolute w-5 h-5 text-blue-400
          transition-all duration-300 ease-in-out
          ${isDark 
            ? 'opacity-100 rotate-0 scale-100' 
            : 'opacity-0 -rotate-90 scale-0'
          }
        `}
      />
      
      {/* Ripple effect on hover */}
      <span 
        className="
          absolute inset-0 rounded-full
          bg-gradient-to-r from-yellow-400 to-blue-400
          opacity-0 group-hover:opacity-20
          transition-opacity duration-300
        "
      />
    </button>
  );
};

export default ThemeToggle;
