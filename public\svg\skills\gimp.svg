<svg width="296" height="256" viewBox="0 0 296 256" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M49.2591 104.638C49.2591 104.638 61.5743 98.4926 61.5743 86.2083C61.5743 73.9239 61.5743 24.768 61.5743 24.768C61.5743 24.768 85.9521 74.632 148.008 65.0385C206.259 56.0362 230.427 34.3615 243.451 1.83718C244.325 -0.354914 247.515 -0.0531954 247.896 2.27437C254.183 40.6114 266.191 180.45 160.096 184.508C160.096 184.508 98.5197 190.653 43.1016 141.503C49.2591 129.213 49.2591 104.638 49.2591 104.638Z" fill="url(#paint0_radial)"/>
<path d="M57.7232 97.6359C70.0938 116.552 69.3056 138.953 55.9622 147.679C42.6187 156.404 21.7815 148.14 9.41091 129.23C-2.95966 110.321 -2.17149 87.9131 11.172 79.1878C24.5154 70.4625 45.3527 78.726 57.7232 97.6359Z" fill="url(#paint1_radial)"/>
<path d="M148.24 117.187C168.393 117.187 184.73 102.025 184.73 83.3208C184.73 64.6167 168.393 49.4541 148.24 49.4541C128.087 49.4541 111.75 64.6167 111.75 83.3208C111.75 102.025 128.087 117.187 148.24 117.187Z" fill="url(#paint2_radial)"/>
<path d="M84.893 111.031C97.7715 111.031 108.212 101.382 108.212 89.4792C108.212 77.5767 97.7715 67.9277 84.893 67.9277C72.0144 67.9277 61.5742 77.5767 61.5742 89.4792C61.5742 101.382 72.0144 111.031 84.893 111.031Z" fill="url(#paint3_radial)"/>
<path d="M157.019 104.873C165.521 104.873 172.413 97.9807 172.413 89.4789C172.413 80.9771 165.521 74.085 157.019 74.085C148.517 74.085 141.625 80.9771 141.625 89.4789C141.625 97.9807 148.517 104.873 157.019 104.873Z" fill="#999999"/>
<path d="M157.018 98.7149C162.119 98.7149 166.254 94.5797 166.254 89.4786C166.254 84.3774 162.119 80.2422 157.018 80.2422C151.917 80.2422 147.781 84.3774 147.781 89.4786C147.781 94.5797 151.917 98.7149 157.018 98.7149Z" fill="#323537"/>
<path d="M89.5668 104.289C95.4739 104.289 100.263 99.5002 100.263 93.5932C100.263 87.6861 95.4739 82.8975 89.5668 82.8975C83.6597 82.8975 78.8711 87.6861 78.8711 93.5932C78.8711 99.5002 83.6597 104.289 89.5668 104.289Z" fill="#999999"/>
<path d="M89.5669 98.6837C92.3793 98.6837 94.6592 96.4037 94.6592 93.5913C94.6592 90.7789 92.3793 88.499 89.5669 88.499C86.7545 88.499 84.4746 90.7789 84.4746 93.5913C84.4746 96.4037 86.7545 98.6837 89.5669 98.6837Z" fill="#323537"/>
<path d="M82.915 93.4882C86.6898 93.4882 89.7499 90.4281 89.7499 86.6533C89.7499 82.8785 86.6898 79.8184 82.915 79.8184C79.1402 79.8184 76.0801 82.8785 76.0801 86.6533C76.0801 90.4281 79.1402 93.4882 82.915 93.4882Z" fill="white"/>
<path d="M21.8112 104.872C28.2114 104.872 33.3998 99.6841 33.3998 93.2839C33.3998 86.8837 28.2114 81.6953 21.8112 81.6953C15.411 81.6953 10.2227 86.8837 10.2227 93.2839C10.2227 99.6841 15.411 104.872 21.8112 104.872Z" fill="url(#paint4_linear)"/>
<path d="M146.309 89.8426C151.971 89.8426 156.561 85.2525 156.561 79.5902C156.561 73.928 151.971 69.3379 146.309 69.3379C140.647 69.3379 136.057 73.928 136.057 79.5902C136.057 85.2525 140.647 89.8426 146.309 89.8426Z" fill="white"/>
<path d="M245.585 5.93359C245.585 5.93359 238.51 33.3471 227.901 47.7928C217.291 62.2385 220.826 79.0363 231.435 81.3947C242.045 83.753 237.039 76.678 232.907 75.2063C228.781 73.7347 223.474 62.8234 231.731 50.7423C239.982 38.6549 244.114 18.9076 245.585 5.93359Z" fill="url(#paint5_linear)"/>
<path d="M195.174 150.963C195.174 150.963 195.174 136.813 180.433 135.637C180.433 135.637 185.039 137.478 187.822 141.696C169.085 158.173 132.977 151.56 132.977 151.56C164.22 160.144 183.678 152.527 189.977 149.3C192.871 152.385 195.174 150.963 195.174 150.963Z" fill="#181612"/>
<path d="M188.694 145.958C198.128 150.379 239.396 178.679 239.396 178.679C239.396 178.679 234.088 184.868 229.667 191.062C218.466 185.169 180.929 152.743 180.929 152.743C180.338 149.788 185.055 144.48 188.694 145.958Z" fill="url(#paint6_linear)"/>
<path d="M269.895 205.204L239.384 178.671C235.554 175.426 226.416 188.991 229.655 191.054L262.228 211.983L269.895 205.204Z" fill="url(#paint7_linear)"/>
<path d="M295.56 256.202C295.56 256.202 257.174 245.198 260.604 214.047C261.392 206.898 268.91 205.352 268.91 205.352C268.91 205.352 287.247 201.079 288.965 227.606C290.117 245.297 295.56 256.202 295.56 256.202Z" fill="url(#paint8_linear)"/>
</g>
<defs>
<radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(138.021 99.617) scale(108.447 108.209)">
<stop stop-color="#A69D88"/>
<stop offset="1" stop-color="#3E3A2E"/>
</radialGradient>
<radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(28.4583 100.463) rotate(-33.1851) scale(35.8903 35.1688)">
<stop stop-color="#7D8587"/>
<stop offset="1" stop-color="#323538"/>
</radialGradient>
<radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(143.134 71.9231) scale(34.9974 35.4368)">
<stop stop-color="#FAFAFB"/>
<stop offset="1" stop-color="#C8CDD1"/>
</radialGradient>
<radialGradient id="paint3_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(81.3585 82.0101) scale(22.4505)">
<stop stop-color="#FAFAFB"/>
<stop offset="1" stop-color="#C8CDD1"/>
</radialGradient>
<linearGradient id="paint4_linear" x1="13.948" y1="83.6781" x2="25.7459" y2="98.0868" gradientUnits="userSpaceOnUse">
<stop stop-color="#CACED2"/>
<stop offset="1" stop-color="#8F979E"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="239.095" y1="32.9961" x2="227.809" y2="79.0856" gradientUnits="userSpaceOnUse">
<stop stop-color="#77705F"/>
<stop offset="1" stop-color="#4B473A"/>
</linearGradient>
<linearGradient id="paint6_linear" x1="213.029" y1="160.484" x2="204.445" y2="173.094" gradientUnits="userSpaceOnUse">
<stop stop-color="#C26715"/>
<stop offset="0.508" stop-color="#B85515"/>
<stop offset="1" stop-color="#AD3F16"/>
</linearGradient>
<linearGradient id="paint7_linear" x1="258.903" y1="183.246" x2="247.992" y2="197.926" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAFAFB"/>
<stop offset="1" stop-color="#C8CDD1"/>
</linearGradient>
<linearGradient id="paint8_linear" x1="286.25" y1="227.477" x2="270.542" y2="238.394" gradientUnits="userSpaceOnUse">
<stop stop-color="#6D7479"/>
<stop offset="1" stop-color="#323538"/>
</linearGradient>
<clipPath id="clip0">
<rect width="295.564" height="256" fill="white"/>
</clipPath>
</defs>
</svg>
