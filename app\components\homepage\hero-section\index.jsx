// @flow strict

import { personalData } from "@/utils/data/personal-data";
import Image from "next/image";
import Link from "next/link";
import { BsGithub, BsLinkedin } from "react-icons/bs";
import { FaFacebook, FaTwitterSquare } from "react-icons/fa";
import { MdDownload } from "react-icons/md";
import { RiContactsFill } from "react-icons/ri";
import { SiLeetcode } from "react-icons/si";
import GlowCard from "../../helper/glow-card";

function HeroSection() {
  return (
    <section className="relative flex flex-col items-center justify-between py-4 lg:py-12">


      <div className="grid items-start grid-cols-1 lg:grid-cols-2 lg:gap-12 gap-y-8">
        <div className="flex flex-col items-start justify-center order-2 p-2 pb-20 lg:order-1 md:pb-10 lg:pt-10">
          <h1 className="text-3xl font-bold leading-10 text-laravelGray-900 dark:text-white md:font-extrabold lg:text-[2.6rem] lg:leading-[3.5rem] transition-colors duration-400">
            Hello, <br />
            This is {' '}
            <span className="text-laravelRed dark:text-laravelRedLight transition-colors duration-400">{personalData.name}</span>
            {` , I'm a Professional `}
            <span className="text-laravelRed dark:text-[#16f2b3] transition-colors duration-400">{personalData.designation}</span>
            .
          </h1>

          <div className="flex items-center gap-5 my-12">
            <Link
              href={personalData.github}
              target='_blank'
              className="text-laravelRed dark:text-pink-500 transition-all duration-300 hover:scale-125 hover:text-laravelRedDark dark:hover:text-pink-400"
            >
              <BsGithub size={30} />
            </Link>
            <Link
              href={personalData.linkedIn}
              target='_blank'
              className="text-laravelRed dark:text-pink-500 transition-all duration-300 hover:scale-125 hover:text-laravelRedDark dark:hover:text-pink-400"
            >
              <BsLinkedin size={30} />
            </Link>
          </div>

          <div className="flex items-center gap-3">
            <Link href="#contact" className="bg-gradient-to-r to-laravelRed from-laravelOrange dark:to-laravelRedLight dark:from-laravelRed p-[1px] rounded-full transition-all duration-300 hover:from-laravelRed hover:to-laravelOrange dark:hover:from-laravelRedLight dark:hover:to-laravelRed">
              <button className="px-3 text-xs md:px-8 py-3 md:py-4 bg-white dark:bg-[#0d1224] rounded-full border-none text-center md:text-sm font-medium uppercase tracking-wider text-laravelGray-900 dark:text-[#ffff] no-underline transition-all duration-200 ease-out md:font-semibold flex items-center gap-1 hover:gap-3">
                <span>Contact me</span>
                <RiContactsFill size={16} />
              </button>
            </Link>

            {/* <Link className="flex items-center gap-1 px-3 py-3 text-xs font-medium tracking-wider text-center text-white no-underline uppercase transition-all duration-200 ease-out rounded-full hover:gap-3 bg-gradient-to-r from-pink-500 to-violet-600 md:px-8 md:py-4 md:text-sm hover:text-white hover:no-underline md:font-semibold" role="button" target="_blank" href={personalData.resume}
            >
              <span>Get Resume</span>
              <MdDownload size={16} />
            </Link> */}
          </div>

        </div>
        <div className="order-1 lg:order-2">
          <GlowCard identifier="hero-editor">
            <div className="from-laravelGray-50 to-laravelGray-100 dark:from-[#0d1224] relative bg-gradient-to-r dark:to-[#0a0d37] transition-all duration-400">
          <div className="flex flex-row">
            <div className="h-[1px] w-full bg-gradient-to-r from-transparent via-laravelRed to-laravelOrange dark:via-laravelRedLight dark:to-laravelRed transition-colors duration-400"></div>
            <div className="h-[1px] w-full bg-gradient-to-r from-laravelOrange to-transparent dark:from-laravelRed transition-colors duration-400"></div>
          </div>
          <div className="px-4 py-5 lg:px-8">
            <div className="flex flex-row space-x-2">
              <div className="w-3 h-3 bg-red-400 rounded-full"></div>
              <div className="w-3 h-3 bg-orange-400 rounded-full"></div>
              <div className="w-3 h-3 bg-green-200 rounded-full"></div>
            </div>
          </div>
          <div className="overflow-hidden border-t-[2px] border-laravelGray-300 dark:border-laravelGray-700 px-4 lg:px-8 py-4 lg:py-8 transition-colors duration-400">
            <code className="font-mono text-xs md:text-sm lg:text-base">
              <div className="blink">
                <span className="mr-2 text-laravelRed dark:text-laravelRedLight transition-colors duration-400">const</span>
                <span className="mr-2 text-laravelGray-900 dark:text-white transition-colors duration-400">coder</span>
                <span className="mr-2 text-laravelRed dark:text-laravelRedLight transition-colors duration-400">=</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{'{'}</span>
              </div>
              <div>
                <span className="ml-4 mr-2 text-laravelGray-900 dark:text-white lg:ml-8 transition-colors duration-400">name:</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{`'`}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">Kamal Abouzayed</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{`',`}</span>
              </div>
              <div className="ml-4 mr-2 lg:ml-8">
                <span className="text-laravelGray-900 dark:text-white transition-colors duration-400">skills:</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{`['`}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">PHP</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{"', '"}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">MySql</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{"', '"}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">Laravel</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{"', '"}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">Restful APIs</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{"', '"}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">Livewire</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{"', '"}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">Javascript</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{"', '"}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">Jquery</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{"', '"}</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">Ajax</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{"'],"}</span>
              </div>
              <div>
                <span className="ml-4 mr-2 text-laravelGray-900 dark:text-white lg:ml-8 transition-colors duration-400">hardWorker:</span>
                <span className="text-laravelRed dark:text-orange-400 transition-colors duration-400">true</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">,</span>
              </div>
              <div>
                <span className="ml-4 mr-2 text-laravelGray-900 dark:text-white lg:ml-8 transition-colors duration-400">quickLearner:</span>
                <span className="text-laravelRed dark:text-orange-400 transition-colors duration-400">true</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">,</span>
              </div>
              <div>
                <span className="ml-4 mr-2 text-laravelGray-900 dark:text-white lg:ml-8 transition-colors duration-400">problemSolver:</span>
                <span className="text-laravelRed dark:text-orange-400 transition-colors duration-400">true</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">,</span>
              </div>
              <div>
                <span className="ml-4 mr-2 text-green-600 dark:text-green-400 lg:ml-8 transition-colors duration-400">hireable:</span>
                <span className="text-laravelRed dark:text-orange-400 transition-colors duration-400">function</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{'() {'}</span>
              </div>
              <div>
                <span className="ml-8 mr-2 text-laravelRed dark:text-orange-400 lg:ml-16 transition-colors duration-400">return</span>
                <span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{`(`}</span>
              </div>
              <div>
                <span className="ml-12 lg:ml-24 text-blue-600 dark:text-cyan-400 transition-colors duration-400">this.</span>
                <span className="mr-2 text-laravelGray-900 dark:text-white transition-colors duration-400">hardWorker</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">&amp;&amp;</span>
              </div>
              <div>
                <span className="ml-12 lg:ml-24 text-blue-600 dark:text-cyan-400 transition-colors duration-400">this.</span>
                <span className="mr-2 text-laravelGray-900 dark:text-white transition-colors duration-400">problemSolver</span>
                <span className="text-laravelOrange dark:text-amber-300 transition-colors duration-400">&amp;&amp;</span>
              </div>
              <div>
                <span className="ml-12 lg:ml-24 text-blue-600 dark:text-cyan-400 transition-colors duration-400">this.</span>
                <span className="mr-2 text-laravelGray-900 dark:text-white transition-colors duration-400">skills.length</span>
                <span className="mr-2 text-laravelOrange dark:text-amber-300 transition-colors duration-400">&gt;=</span>
                <span className="text-laravelRed dark:text-orange-400 transition-colors duration-400">5</span>
              </div>
              <div><span className="ml-8 mr-2 text-laravelGray-600 dark:text-gray-400 lg:ml-16 transition-colors duration-400">{`);`}</span></div>
              <div><span className="ml-4 text-laravelGray-600 dark:text-gray-400 lg:ml-8 transition-colors duration-400">{`};`}</span></div>
              <div><span className="text-laravelGray-600 dark:text-gray-400 transition-colors duration-400">{`};`}</span></div>
            </code>
          </div>
            </div>
          </GlowCard>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;