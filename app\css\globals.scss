@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light theme variables */
  --foreground-rgb: 30, 41, 59; /* slate-800 */
  --background-rgb: 255, 255, 255; /* white */
  --surface-rgb: 248, 250, 252; /* slate-50 */
  --accent-rgb: 14, 165, 233; /* sky-500 */
}

.dark {
  /* Dark theme variables */
  --foreground-rgb: 241, 245, 249; /* slate-100 */
  --background-rgb: 13, 18, 36; /* custom dark blue */
  --surface-rgb: 30, 41, 59; /* slate-800 */
  --accent-rgb: 22, 242, 179; /* custom green */
}

/* Smooth transitions for theme changes */
*,
*::before,
*::after {
  transition:
    background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent transition on page load */
.preload * {
  transition: none !important;
}

html {
  /* Prevent flash of unstyled content */
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
}

html.loaded {
  visibility: visible;
  opacity: 1;
}

body {
  color: rgb(var(--foreground-rgb));
  background-color: rgb(var(--background-rgb));
  /* Ensure smooth scrolling */
  scroll-behavior: smooth;
  /* Prevent horizontal scroll */
  overflow-x: hidden;

  /* Subtle dotted pattern overlay */
  background-image:
    radial-gradient(circle at 1px 1px, rgba(var(--foreground-rgb), 0.08) 1px, transparent 0);
  background-size: 20px 20px;
  background-attachment: fixed;
}

/* Adjust pattern opacity for dark mode */
.dark body {
  background-image:
    radial-gradient(circle at 1px 1px, rgba(var(--foreground-rgb), 0.05) 1px, transparent 0);
}

// #sticky-card-1 {
//   --index: 1;
// }

// #sticky-card-2 {
//   --index: 2;
// }

// #sticky-card-3 {
//   --index: 3;
// }

// #sticky-card-4 {
//   --index: 4;
// }

.sticky-card {
  // padding-top: calc(var(--index) * 2.5rem);
  top: calc(var(--index) * 4rem);
}