@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light theme variables */
  --foreground-rgb: 30, 41, 59; /* slate-800 */
  --background-rgb: 255, 255, 255; /* white */
  --surface-rgb: 248, 250, 252; /* slate-50 */
  --accent-rgb: 14, 165, 233; /* sky-500 */
}

.dark {
  /* Dark theme variables */
  --foreground-rgb: 241, 245, 249; /* slate-100 */
  --background-rgb: 13, 18, 36; /* custom dark blue */
  --surface-rgb: 30, 41, 59; /* slate-800 */
  --accent-rgb: 22, 242, 179; /* custom green */
}

* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

body {
  color: rgb(var(--foreground-rgb));
  background-color: rgb(var(--background-rgb));
  transition: background-color 0.3s ease, color 0.3s ease;
}

// #sticky-card-1 {
//   --index: 1;
// }

// #sticky-card-2 {
//   --index: 2;
// }

// #sticky-card-3 {
//   --index: 3;
// }

// #sticky-card-4 {
//   --index: 4;
// }

.sticky-card {
  // padding-top: calc(var(--index) * 2.5rem);
  top: calc(var(--index) * 4rem);
}