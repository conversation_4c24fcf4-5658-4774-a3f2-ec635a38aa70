/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        laravelRed: '#FF2D20',
        laravelOrange: '#FF8C42',
        laravelYellow: '#FFD93D',
        laravelGreen: '#6CB2EB',
        laravelBlue: '#4F46E5',
        // Light theme colors
        light: {
          bg: '#ffffff',
          surface: '#f8fafc',
          primary: '#1e293b',
          secondary: '#64748b',
          accent: '#0ea5e9',
        },
        // Dark theme colors (existing)
        dark: {
          bg: '#0d1224',
          surface: '#1e293b',
          primary: '#f1f5f9',
          secondary: '#cbd5e1',
          accent: '#16f2b3',
        },
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      container: {
        center: true,
        padding: {
          DEFAULT: "1rem",
          sm: "2rem",
          lg: "3rem",
          xl: "4rem",
          "2xl": "4rem",
          "3xl": "5rem",
        },
      },
      screens: {
        "4k": "1980px",
      },
      transitionProperty: {
        'theme': 'background-color, border-color, color, fill, stroke',
      },
    },
  },
  plugins: [],
}
