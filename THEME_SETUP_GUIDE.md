# Light/Dark Mode Toggle System - Setup Guide

## Overview
This guide documents the comprehensive light/dark mode toggle system implemented for your Next.js portfolio project. The system includes Laravel-inspired colors, smooth transitions, accessibility features, and proper SSR handling.

## Files Modified/Created

### 1. **Tailwind Configuration** (`tailwind.config.js`)
- Added `darkMode: 'class'` strategy
- Added Laravel-inspired color palette
- Added custom light/dark theme colors
- Added transition properties for smooth theme changes

### 2. **Theme Context** (`app/contexts/ThemeContext.jsx`)
- React Context for global theme state management
- localStorage persistence for user preferences
- System preference detection as fallback
- Proper SSR/hydration handling to prevent flicker

### 3. **Theme Toggle Component** (`app/components/ThemeToggle.jsx`)
- Accessible toggle button with sun/moon icons
- Smooth animations and transitions
- Keyboard navigation support
- Screen reader friendly

### 4. **Global Styles** (`app/css/globals.scss`)
- CSS custom properties for theme variables
- Smooth transitions for all theme changes
- Support for both light and dark themes

### 5. **Layout Updates** (`app/layout.js`)
- Wrapped application with ThemeProvider
- Updated main container classes for theme support

### 6. **Navbar Updates** (`app/components/navbar.jsx`)
- Added ThemeToggle component
- Updated navigation links with theme-aware colors
- Added Laravel red branding for light mode

## Usage Examples

### Basic Theme-Aware Styling
```jsx
// Text colors
<h1 className="text-gray-900 dark:text-white">
  Heading
</h1>

// Background colors
<div className="bg-white dark:bg-gray-800">
  Content
</div>

// Borders
<div className="border border-gray-200 dark:border-gray-700">
  Card
</div>
```

### Using Custom Colors
```jsx
// Laravel red (works in both themes)
<span className="text-laravelRed">Laravel</span>

// Custom theme colors
<div className="bg-light-surface dark:bg-dark-surface">
  Surface
</div>
```

### Using Theme Context
```jsx
import { useTheme } from '@/app/contexts/ThemeContext';

function MyComponent() {
  const { theme, toggleTheme, isDark, isLight } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <button onClick={toggleTheme}>
        Switch to {isDark ? 'light' : 'dark'} mode
      </button>
    </div>
  );
}
```

## Color Palette

### Laravel Colors
- `laravelRed`: #FF2D20
- `laravelOrange`: #FF8C42
- `laravelYellow`: #FFD93D
- `laravelGreen`: #6CB2EB
- `laravelBlue`: #4F46E5

### Light Theme Colors
- `light.bg`: #ffffff
- `light.surface`: #f8fafc
- `light.primary`: #1e293b
- `light.secondary`: #64748b
- `light.accent`: #0ea5e9

### Dark Theme Colors
- `dark.bg`: #0d1224
- `dark.surface`: #1e293b
- `dark.primary`: #f1f5f9
- `dark.secondary`: #cbd5e1
- `dark.accent`: #16f2b3

## Best Practices

### 1. Always Use Dark Variants
```jsx
// Good
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white">

// Avoid
<div className="bg-white text-gray-900">
```

### 2. Include Transitions
```jsx
<div className="transition-colors duration-300 bg-white dark:bg-gray-800">
```

### 3. Maintain Contrast Ratios
- Ensure sufficient contrast in both themes
- Test with accessibility tools
- Use semantic color names when possible

### 4. Handle Loading States
```jsx
const { theme } = useTheme();

// Prevent hydration mismatch
if (!mounted) return <div>Loading...</div>;
```

## Accessibility Features

- **Keyboard Navigation**: Theme toggle is fully keyboard accessible
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Clear focus indicators in both themes
- **Color Contrast**: WCAG compliant contrast ratios
- **Reduced Motion**: Respects user's motion preferences

## Browser Support

- **Modern Browsers**: Full support for all features
- **localStorage**: Graceful fallback to system preference
- **CSS Custom Properties**: Supported in all target browsers
- **Prefers Color Scheme**: Automatic system theme detection

## Testing Checklist

- [ ] Theme toggle works correctly
- [ ] Theme persists after page reload
- [ ] System preference detection works
- [ ] No hydration mismatches
- [ ] Smooth transitions between themes
- [ ] All components respect theme changes
- [ ] Accessibility features work properly
- [ ] Colors have sufficient contrast

## Troubleshooting

### Hydration Mismatch
If you see hydration warnings, ensure components using `useTheme` handle the loading state properly.

### Theme Not Persisting
Check that localStorage is available and not blocked by browser settings.

### Transitions Not Working
Ensure the `transition-colors` class is applied to elements that change color.

### Custom Colors Not Working
Verify that custom colors are properly defined in `tailwind.config.js` and the build process has been restarted.

## Integration with Existing Components

To add theme support to existing components:

1. Add dark variants to existing classes
2. Include transition classes for smooth changes
3. Test in both light and dark modes
4. Ensure accessibility standards are maintained

Example transformation:
```jsx
// Before
<div className="bg-white text-gray-900 border border-gray-200">

// After
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 transition-colors duration-300">
```
